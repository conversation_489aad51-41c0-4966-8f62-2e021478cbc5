# E-Learning Platform

A comprehensive e-learning platform built with Next.js, Prisma, and SQLite. Features include course management, quizzes, discussions, and user progress tracking.

## Features

- 🎓 Course creation and management
- 📚 Module and lesson organization
- ❓ Interactive quizzes with multiple question types
- 💬 Discussion forums
- 📊 Progress tracking
- 👥 User roles (Student, Teacher, Admin)
- 🔐 Authentication with NextAuth.js

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Set up the database:
```bash
npm run db:setup
```

4. Run the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Database Scripts

- `npm run db:setup` - Set up development database
- `npm run db:seed` - Seed database with sample data
- `npm run db:prod` - Create production database locally

## Deploy on Vercel

This project is configured for deployment on Vercel with a SQLite database.

### Environment Variables

Set the following environment variables in your Vercel dashboard:

```bash
# Database
DATABASE_URL="file:./prisma/prod.db"

# NextAuth.js
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="https://your-app-name.vercel.app"

# File Upload
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE=10485760
```

### Deployment Process

1. The build process automatically:
   - Creates a production SQLite database (`prod.db`)
   - Seeds it with sample data
   - Includes the database in the deployment

2. The database contains:
   - Admin user: `<EMAIL>` (password: `password123`)
   - 3 teachers and 5 students
   - Sample courses with lessons and quizzes
   - Discussion forums

### Important Notes

- The SQLite database is bundled with the deployment
- Database changes in production are not persistent across deployments
- For production use, consider migrating to a persistent database solution

## Demo Credentials

- **Admin**: `<EMAIL>` / `password123`
- **Teacher**: `<EMAIL>` / `password123`
- **Student**: `<EMAIL>` / `password123`
