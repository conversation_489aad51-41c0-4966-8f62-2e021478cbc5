import { PrismaClient, Role } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting comprehensive database seeding...')

  // Clear existing data
  await prisma.submission.deleteMany()
  await prisma.question.deleteMany()
  await prisma.quiz.deleteMany()
  await prisma.progress.deleteMany()
  await prisma.reply.deleteMany()
  await prisma.discussion.deleteMany()
  await prisma.lesson.deleteMany()
  await prisma.module.deleteMany()
  await prisma.enrollment.deleteMany()
  await prisma.course.deleteMany()
  await prisma.user.deleteMany()

  console.log('🗑️ Cleared existing data')

  // Create users with common password
  const hashedPassword = await bcrypt.hash('password123', 10)

  // Create admin user
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Admin User',
      role: Role.ADMIN,
    },
  })

  // Create teachers
  const teacher1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Dr. <PERSON>',
      role: Role.TEACHER,
    },
  })

  const teacher2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Prof. Sarah Wilson',
      role: Role.TEACHER,
    },
  })

  const teacher3 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Dr. Michael Chen',
      role: Role.TEACHER,
    },
  })

  // Create students
  const students = await Promise.all([
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Jane Doe',
        role: Role.STUDENT,
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Mike Johnson',
        role: Role.STUDENT,
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Emily Brown',
        role: Role.STUDENT,
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Alex Garcia',
        role: Role.STUDENT,
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Lisa Wang',
        role: Role.STUDENT,
      },
    }),
  ])

  console.log('✅ Users created')

  // Create comprehensive courses
  const webDevCourse = await prisma.course.create({
    data: {
      id: 'web-dev-fundamentals',
      title: 'Complete Web Development Bootcamp',
      description: 'Master modern web development from scratch. Learn HTML5, CSS3, JavaScript ES6+, React, Node.js, and build real-world projects. Perfect for beginners who want to become full-stack developers.',
      category: 'Programming',
      tags: JSON.stringify(['HTML', 'CSS', 'JavaScript', 'React', 'Node.js', 'Full Stack']),
      price: 199.99,
      isPaid: true,
      isPublished: true,
      teacherId: teacher1.id,
    },
  })

  const dataScience = await prisma.course.create({
    data: {
      id: 'data-science-mastery',
      title: 'Data Science and Machine Learning Mastery',
      description: 'Comprehensive data science course covering Python, statistics, machine learning, deep learning, and data visualization. Work with real datasets and build predictive models.',
      category: 'Data Science',
      tags: JSON.stringify(['Python', 'Machine Learning', 'Statistics', 'Pandas', 'Scikit-learn', 'TensorFlow']),
      price: 249.99,
      isPaid: true,
      isPublished: true,
      teacherId: teacher2.id,
    },
  })

  const digitalMarketing = await prisma.course.create({
    data: {
      id: 'digital-marketing-pro',
      title: 'Digital Marketing Professional Certificate',
      description: 'Learn proven digital marketing strategies used by top companies. Master SEO, social media marketing, content marketing, email marketing, and analytics.',
      category: 'Marketing',
      tags: JSON.stringify(['SEO', 'Social Media', 'Content Marketing', 'Email Marketing', 'Analytics']),
      price: 0,
      isPaid: false,
      isPublished: true,
      teacherId: teacher3.id,
    },
  })

  console.log('✅ Courses created')

  // Create modules and lessons for Web Development Course
  const htmlModule = await prisma.module.create({
    data: {
      title: 'HTML Fundamentals',
      description: 'Learn the building blocks of web pages with HTML5',
      order: 1,
      courseId: webDevCourse.id,
    },
  })

  const cssModule = await prisma.module.create({
    data: {
      title: 'CSS Styling and Layout',
      description: 'Master CSS for beautiful and responsive designs',
      order: 2,
      courseId: webDevCourse.id,
    },
  })

  const jsModule = await prisma.module.create({
    data: {
      title: 'JavaScript Programming',
      description: 'Add interactivity and dynamic behavior to your websites',
      order: 3,
      courseId: webDevCourse.id,
    },
  })

  // HTML Module Lessons
  const htmlIntro = await prisma.lesson.create({
    data: {
      title: 'Introduction to HTML',
      content: `**Welcome to HTML!**

HTML (HyperText Markup Language) is the standard markup language for creating web pages. It describes the structure of a web page using markup tags.

**What you'll learn:**
- HTML document structure
- Common HTML elements
- Semantic HTML
- Best practices

**Key Concepts:**
HTML uses *elements* to structure content. Elements are defined by tags, which are keywords surrounded by angle brackets like \`<tagname>\`.

Most HTML elements have an opening tag and a closing tag:
\`<tagname>Content goes here</tagname>\`

**Basic HTML Structure:**
Every HTML document follows this basic structure:

\`\`\`html
<!DOCTYPE html>
<html>
<head>
    <title>Page Title</title>
</head>
<body>
    <h1>My First Heading</h1>
    <p>My first paragraph.</p>
</body>
</html>
\`\`\`

This lesson provides the foundation for everything else you'll learn in web development!`,
      type: 'TEXT',
      order: 1,
      moduleId: htmlModule.id,
      attachments: JSON.stringify([]),
    },
  })

  const htmlElements = await prisma.lesson.create({
    data: {
      title: 'HTML Elements and Tags',
      content: `**HTML Elements Deep Dive**

HTML elements are the building blocks of web pages. Let's explore the most important ones:

**Headings:**
HTML provides six levels of headings, from \`<h1>\` (most important) to \`<h6>\` (least important):

\`\`\`html
<h1>Main Title</h1>
<h2>Section Title</h2>
<h3>Subsection Title</h3>
\`\`\`

**Paragraphs and Text:**
- \`<p>\` for paragraphs
- \`<strong>\` for **important text**
- \`<em>\` for *emphasized text*
- \`<br>\` for line breaks

**Lists:**
*Unordered lists:*
\`\`\`html
<ul>
    <li>First item</li>
    <li>Second item</li>
    <li>Third item</li>
</ul>
\`\`\`

*Ordered lists:*
\`\`\`html
<ol>
    <li>Step one</li>
    <li>Step two</li>
    <li>Step three</li>
</ol>
\`\`\`

**Links and Images:**
\`\`\`html
<a href="https://example.com">Visit Example</a>
<img src="image.jpg" alt="Description of image">
\`\`\`

**Practice Exercise:**
Try creating a simple webpage with:
1. A main heading
2. Two paragraphs of text
3. A list of your hobbies
4. A link to your favorite website

Remember: Good HTML is semantic and accessible!`,
      type: 'TEXT',
      order: 2,
      moduleId: htmlModule.id,
      attachments: JSON.stringify([]),
    },
  })

  // Create HTML Quiz
  const htmlQuiz = await prisma.quiz.create({
    data: {
      title: 'HTML Fundamentals Quiz',
      description: 'Test your understanding of HTML basics',
      timeLimit: 15,
      lessonId: htmlElements.id,
    },
  })

  // HTML Quiz Questions
  await prisma.question.createMany({
    data: [
      {
        text: 'What does HTML stand for?',
        type: 'MULTIPLE_CHOICE',
        options: JSON.stringify([
          'HyperText Markup Language',
          'High Tech Modern Language',
          'Home Tool Markup Language',
          'Hyperlink and Text Markup Language'
        ]),
        correctAnswer: 'HyperText Markup Language',
        points: 2,
        quizId: htmlQuiz.id,
      },
      {
        text: 'Which HTML element is used for the largest heading?',
        type: 'MULTIPLE_CHOICE',
        options: JSON.stringify(['<h1>', '<h6>', '<heading>', '<header>']),
        correctAnswer: '<h1>',
        points: 1,
        quizId: htmlQuiz.id,
      },
      {
        text: 'HTML elements must always have both opening and closing tags.',
        type: 'TRUE_FALSE',
        options: JSON.stringify(['True', 'False']),
        correctAnswer: 'False',
        points: 1,
        quizId: htmlQuiz.id,
      },
      {
        text: 'What attribute is used to provide alternative text for images?',
        type: 'SHORT_ANSWER',
        options: JSON.stringify([]),
        correctAnswer: 'alt',
        points: 2,
        quizId: htmlQuiz.id,
      },
    ],
  })

  console.log('✅ HTML Module created with lessons and quiz')

  // CSS Module Lessons
  await prisma.lesson.create({
    data: {
      title: 'CSS Basics and Selectors',
      content: `**Introduction to CSS**

CSS (Cascading Style Sheets) is used to style and layout web pages. It controls how HTML elements are displayed.

**CSS Syntax:**
CSS rules consist of a selector and a declaration block:

\`\`\`css
selector {
    property: value;
    property: value;
}
\`\`\`

**Common Selectors:**

*Element Selector:*
\`\`\`css
p {
    color: blue;
    font-size: 16px;
}
\`\`\`

*Class Selector:*
\`\`\`css
.highlight {
    background-color: yellow;
    font-weight: bold;
}
\`\`\`

*ID Selector:*
\`\`\`css
#header {
    background-color: navy;
    color: white;
}
\`\`\`

**Common Properties:**
- \`color\` - text color
- \`background-color\` - background color
- \`font-size\` - text size
- \`margin\` - space outside element
- \`padding\` - space inside element
- \`border\` - element border

**Box Model:**
Every HTML element is essentially a box consisting of:
1. **Content** - the actual content
2. **Padding** - space around content
3. **Border** - border around padding
4. **Margin** - space outside border

Understanding the box model is crucial for CSS layout!`,
      type: 'TEXT',
      order: 1,
      moduleId: cssModule.id,
      attachments: JSON.stringify([]),
    },
  })

  await prisma.lesson.create({
    data: {
      title: 'CSS Layout and Flexbox',
      content: `**CSS Layout Fundamentals**

CSS provides several methods for laying out elements on a page. Let's focus on **Flexbox**, one of the most powerful layout tools.

**Flexbox Basics:**
Flexbox is a one-dimensional layout method for laying out items in rows or columns.

\`\`\`css
.container {
    display: flex;
    justify-content: center;
    align-items: center;
}
\`\`\`

**Key Flexbox Properties:**

*For the container (flex parent):*
- \`display: flex\` - creates a flex container
- \`flex-direction\` - row, column, row-reverse, column-reverse
- \`justify-content\` - main axis alignment (center, space-between, space-around)
- \`align-items\` - cross axis alignment (center, flex-start, flex-end)
- \`flex-wrap\` - wrap, nowrap, wrap-reverse

*For the items (flex children):*
- \`flex-grow\` - how much item should grow
- \`flex-shrink\` - how much item should shrink
- \`flex-basis\` - initial size before growing/shrinking

**Practical Example:**
\`\`\`css
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
}

.nav-links {
    display: flex;
    gap: 1rem;
}
\`\`\`

**Responsive Design:**
Use media queries to make your layouts responsive:

\`\`\`css
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }
}
\`\`\`

Flexbox makes creating responsive layouts much easier than traditional methods!`,
      type: 'TEXT',
      order: 2,
      moduleId: cssModule.id,
      attachments: JSON.stringify([]),
    },
  })

  // JavaScript Module Lessons
  await prisma.lesson.create({
    data: {
      title: 'JavaScript Fundamentals',
      content: `**Welcome to JavaScript!**

JavaScript is a programming language that adds interactivity and dynamic behavior to web pages.

**Variables and Data Types:**

\`\`\`javascript
// Variables
let name = "John";
const age = 25;
var city = "New York";

// Data Types
let text = "Hello World";        // String
let number = 42;                 // Number
let isActive = true;             // Boolean
let items = [1, 2, 3, 4];       // Array
let person = {                   // Object
    name: "Alice",
    age: 30
};
\`\`\`

**Functions:**
Functions are reusable blocks of code:

\`\`\`javascript
// Function declaration
function greet(name) {
    return "Hello, " + name + "!";
}

// Arrow function
const add = (a, b) => a + b;

// Function call
console.log(greet("World"));
console.log(add(5, 3));
\`\`\`

**DOM Manipulation:**
JavaScript can interact with HTML elements:

\`\`\`javascript
// Select elements
const button = document.getElementById("myButton");
const paragraph = document.querySelector(".text");

// Change content
paragraph.textContent = "New text content";

// Add event listener
button.addEventListener("click", function() {
    alert("Button clicked!");
});
\`\`\`

**Conditional Statements:**
\`\`\`javascript
if (age >= 18) {
    console.log("Adult");
} else {
    console.log("Minor");
}
\`\`\`

**Loops:**
\`\`\`javascript
// For loop
for (let i = 0; i < 5; i++) {
    console.log(i);
}

// Array iteration
items.forEach(item => {
    console.log(item);
});
\`\`\`

JavaScript brings your web pages to life!`,
      type: 'TEXT',
      order: 1,
      moduleId: jsModule.id,
      attachments: JSON.stringify([]),
    },
  })

  console.log('✅ All modules and lessons created')

  // Create enrollments
  await prisma.enrollment.createMany({
    data: [
      { userId: students[0].id, courseId: webDevCourse.id, progress: 75 },
      { userId: students[1].id, courseId: webDevCourse.id, progress: 45 },
      { userId: students[2].id, courseId: webDevCourse.id, progress: 20 },
      { userId: students[0].id, courseId: dataScience.id, progress: 30 },
      { userId: students[1].id, courseId: dataScience.id, progress: 60 },
      { userId: students[3].id, courseId: digitalMarketing.id, progress: 90 },
      { userId: students[4].id, courseId: digitalMarketing.id, progress: 100 },
    ],
  })

  // Create some progress records
  await prisma.progress.createMany({
    data: [
      { userId: students[0].id, lessonId: htmlIntro.id, isCompleted: true, completedAt: new Date() },
      { userId: students[0].id, lessonId: htmlElements.id, isCompleted: true, completedAt: new Date() },
      { userId: students[1].id, lessonId: htmlIntro.id, isCompleted: true, completedAt: new Date() },
    ],
  })

  // Create sample discussions
  const discussion1 = await prisma.discussion.create({
    data: {
      title: 'Question about HTML semantics',
      content: 'I\'m confused about when to use <section> vs <div>. Can someone explain the difference?',
      authorId: students[0].id,
      courseId: webDevCourse.id,
    },
  })

  await prisma.reply.create({
    data: {
      content: 'Great question! <section> is semantic and represents a distinct section of content, while <div> is just a generic container with no semantic meaning.',
      authorId: teacher1.id,
      discussionId: discussion1.id,
    },
  })

  console.log('✅ Enrollments, progress, and discussions created')

  console.log('🎉 Comprehensive database seeding completed!')
  console.log('📚 Created courses with rich content, quizzes, and user interactions')
  console.log('👥 Users: admin, 3 teachers, 5 students')
  console.log('🎓 Courses: Web Development, Data Science, Digital Marketing')
  console.log('📝 Lessons with detailed, readable content')
  console.log('❓ Quizzes with multiple question types')
  console.log('💬 Sample discussions and replies')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
