// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

// User roles and authentication
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      Role     @default(STUDENT)
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  coursesCreated Course[]         @relation("TeacherCourses")
  enrollments    Enrollment[]
  submissions    Submission[]
  discussions    Discussion[]
  replies        Reply[]
  progress       Progress[]
}

enum Role {
  STUDENT
  TEACHER
  ADMIN
}

// Course structure
model Course {
  id          String   @id @default(cuid())
  title       String
  description String
  thumbnail   String?
  category    String
  tags        String   // JSON string for prototype
  price       Float    @default(0)
  isPaid      Boolean  @default(false)
  isPublished Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  teacherId    String
  teacher      User         @relation("TeacherCourses", fields: [teacherId], references: [id])
  modules      Module[]
  enrollments  Enrollment[]
  discussions  Discussion[]
}

// Course modules/chapters
model Module {
  id          String   @id @default(cuid())
  title       String
  description String?
  order       Int
  createdAt   DateTime @default(now())

  courseId String
  course   Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)
  lessons  Lesson[]
}

// Individual lessons
model Lesson {
  id          String      @id @default(cuid())
  title       String
  content     String      // Rich text content
  videoUrl    String?
  attachments String      // JSON string for file paths
  order       Int
  type        LessonType  @default(TEXT)
  createdAt   DateTime    @default(now())

  moduleId String
  module   Module       @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  quizzes  Quiz[]
  progress Progress[]
}

enum LessonType {
  TEXT
  VIDEO
  QUIZ
  ASSIGNMENT
}

// Student enrollments
model Enrollment {
  id          String   @id @default(cuid())
  enrolledAt  DateTime @default(now())
  completedAt DateTime?
  progress    Float    @default(0) // Percentage

  userId   String
  user     User   @relation(fields: [userId], references: [id])
  courseId String
  course   Course @relation(fields: [courseId], references: [id])

  @@unique([userId, courseId])
}

// Progress tracking
model Progress {
  id          String   @id @default(cuid())
  isCompleted Boolean  @default(false)
  completedAt DateTime?
  timeSpent   Int      @default(0) // in minutes

  userId   String
  user     User   @relation(fields: [userId], references: [id])
  lessonId String
  lesson   Lesson @relation(fields: [lessonId], references: [id])

  @@unique([userId, lessonId])
}

// Quiz system
model Quiz {
  id          String     @id @default(cuid())
  title       String
  description String?
  questions   Question[]
  timeLimit   Int?       // in minutes
  createdAt   DateTime   @default(now())

  lessonId    String
  lesson      Lesson       @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  submissions Submission[]
}

model Question {
  id            String       @id @default(cuid())
  text          String
  type          QuestionType @default(MULTIPLE_CHOICE)
  options       String       // JSON string for options
  correctAnswer String
  points        Int          @default(1)

  quizId String
  quiz   Quiz   @relation(fields: [quizId], references: [id], onDelete: Cascade)
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  SHORT_ANSWER
}

// Quiz submissions
model Submission {
  id          String   @id @default(cuid())
  answers     String   // JSON string for answers
  score       Float?
  submittedAt DateTime @default(now())

  userId String
  user   User   @relation(fields: [userId], references: [id])
  quizId String
  quiz   Quiz   @relation(fields: [quizId], references: [id])
}

// Discussion forums
model Discussion {
  id        String   @id @default(cuid())
  title     String
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  authorId String
  author   User   @relation(fields: [authorId], references: [id])
  courseId String
  course   Course @relation(fields: [courseId], references: [id])
  replies  Reply[]
}

model Reply {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())

  authorId     String
  author       User       @relation(fields: [authorId], references: [id])
  discussionId String
  discussion   Discussion @relation(fields: [discussionId], references: [id])
}
