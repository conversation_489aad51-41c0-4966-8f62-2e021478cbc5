#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 Setting up production database...');

try {
  console.log('🔧 Using DATABASE_URL:', process.env.DATABASE_URL);
  
  // Generate Prisma Client
  console.log('🔄 Generating Prisma Client...');
  execSync('npx prisma generate', { stdio: 'inherit' });
  
  // Create production database
  console.log('📊 Creating production database...');
  execSync('npx prisma db push --force-reset', { stdio: 'inherit' });
  
  // Seed the database
  console.log('🌱 Seeding production database...');
  execSync('npm run db:seed', { stdio: 'inherit' });
  
  console.log('🎉 Production database setup completed successfully!');
  
} catch (error) {
  console.error('❌ Production database setup failed:', error.message);
  process.exit(1);
}
