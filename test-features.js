// Simple test script to verify key features
const baseUrl = 'http://localhost:3000'

const tests = [
  {
    name: 'Home page loads',
    url: '/',
    expectedStatus: 200
  },
  {
    name: 'Login page loads',
    url: '/login',
    expectedStatus: 200
  },
  {
    name: 'Course catalog loads',
    url: '/courses',
    expectedStatus: 200
  },
  {
    name: 'Sample course page loads',
    url: '/courses/sample-course-1',
    expectedStatus: 200
  },
  {
    name: 'Course discussions page loads',
    url: '/courses/sample-course-1/discussions',
    expectedStatus: 200
  },
  {
    name: 'API: Courses endpoint (public)',
    url: '/api/courses',
    expectedStatus: 200
  },
  {
    name: 'API: Discussions endpoint (requires auth)',
    url: '/api/discussions?courseId=sample-course-1',
    expectedStatus: 401 // Should be unauthorized without login
  },
  {
    name: 'API: Admin courses endpoint (requires admin)',
    url: '/api/admin/courses',
    expectedStatus: 401 // Should be unauthorized without admin login
  }
]

async function runTests() {
  console.log('🧪 Running E-Learning Platform Feature Tests\n')
  
  let passed = 0
  let failed = 0
  
  for (const test of tests) {
    try {
      const response = await fetch(`${baseUrl}${test.url}`)
      const status = response.status
      
      if (status === test.expectedStatus) {
        console.log(`✅ ${test.name} - Status: ${status}`)
        passed++
      } else {
        console.log(`❌ ${test.name} - Expected: ${test.expectedStatus}, Got: ${status}`)
        failed++
      }
    } catch (error) {
      console.log(`❌ ${test.name} - Error: ${error.message}`)
      failed++
    }
  }
  
  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`)
  
  if (failed === 0) {
    console.log('🎉 All tests passed! The e-learning platform is working correctly.')
  } else {
    console.log('⚠️  Some tests failed. Please check the issues above.')
  }
}

// Run tests if this script is executed directly
if (typeof window === 'undefined') {
  runTests()
}

module.exports = { runTests }
