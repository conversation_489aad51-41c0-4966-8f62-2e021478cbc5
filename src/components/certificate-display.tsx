'use client';

import { Award, Calendar, User, BookOpen } from 'lucide-react';

interface CertificateDisplayProps {
  enrollment: {
    id: string;
    course: {
      title: string;
      description: string;
      teacher: {
        name: string;
      };
    };
    user: {
      name: string;
    };
    completedAt: Date;
  };
  certificateId: string;
}

export function CertificateDisplay({ enrollment, certificateId }: CertificateDisplayProps) {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow-2xl rounded-lg overflow-hidden">
          <div className="bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-6">
            <div className="text-center">
              <Award className="mx-auto h-16 w-16 text-white mb-4" />
              <h1 className="text-3xl font-bold text-white">Certificate of Completion</h1>
              <p className="text-indigo-100 mt-2">E-Learning Platform</p>
            </div>
          </div>
          <div className="p-8">
            <div className="text-center mb-8">
              <p className="text-gray-600 mb-2">This is to certify that</p>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">{enrollment.user.name}</h2>
              <p className="text-gray-600">has successfully completed the course</p>
              <h3 className="text-2xl font-semibold text-indigo-600 mt-2">{enrollment.course.title}</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <User className="h-5 w-5 text-indigo-600 mr-2" />
                  <div>
                    <p className="text-sm text-gray-500">Instructor</p>
                    <p className="font-medium">{enrollment.course.teacher.name}</p>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-indigo-600 mr-2" />
                  <div>
                    <p className="text-sm text-gray-500">Date Completed</p>
                    <p className="font-medium">
                      {new Date(enrollment.completedAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-200 pt-6 mt-8">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Certificate ID</p>
                  <p className="font-mono text-sm">{certificateId}</p>
                </div>
                <button
                  onClick={() => window.print()}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                  Print Certificate
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
