import { prisma } from '@/lib/db'
import { notFound } from 'next/navigation'
import { CertificateDisplay } from '@/components/certificate-display'

interface CertificatePageProps {
  params: Promise<{ id: string }>
}

export default async function CertificatePage({ params }: CertificatePageProps) {
  const { id } = await params
  // Get enrollment details
  const enrollment = await prisma.enrollment.findUnique({
    where: { id },
    include: {
      course: {
        include: {
          teacher: {
            select: {
              name: true
            }
          }
        }
      },
      user: {
        select: {
          name: true
        }
      }
    }
  })

  if (!enrollment || !enrollment.completedAt || enrollment.progress < 100) {
    notFound()
  }

  const certificateId = `CERT-${enrollment.id.toUpperCase()}`

  return (
    <CertificateDisplay 
      enrollment={enrollment}
      certificateId={certificateId}
    />
  )
}
