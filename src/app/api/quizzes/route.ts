import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { z } from 'zod'

const createQuizSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  lessonId: z.string().min(1, 'Lesson ID is required'),
  timeLimit: z.number().optional(),
  questions: z.array(z.object({
    text: z.string().min(1, 'Question text is required'),
    type: z.enum(['MULTIPLE_CHOICE', 'TRUE_FALSE', 'SHORT_ANSWER']),
    options: z.array(z.string()).optional(),
    correctAnswer: z.string().min(1, 'Correct answer is required'),
    points: z.number().min(1).default(1),
  })).min(1, 'At least one question is required'),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !['TEACHER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { title, description, lessonId, timeLimit, questions } = createQuizSchema.parse(body)

    // Verify the lesson exists and user has permission
    const lesson = await prisma.lesson.findUnique({
      where: { id: lessonId },
      include: {
        module: {
          include: {
            course: true
          }
        }
      }
    })

    if (!lesson) {
      return NextResponse.json({ error: 'Lesson not found' }, { status: 404 })
    }

    // Check if user can create quiz for this lesson
    const canCreate = session.user.role === 'ADMIN' || 
                     lesson.module.course.teacherId === session.user.id

    if (!canCreate) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Create quiz with questions
    const quiz = await prisma.quiz.create({
      data: {
        title,
        description,
        lessonId,
        timeLimit,
        questions: {
          create: questions.map(q => ({
            text: q.text,
            type: q.type,
            options: JSON.stringify(q.options || []),
            correctAnswer: q.correctAnswer,
            points: q.points,
          }))
        }
      },
      include: {
        questions: true,
        lesson: {
          include: {
            module: {
              include: {
                course: true
              }
            }
          }
        }
      }
    })

    // Transform questions to include parsed options
    const transformedQuiz = {
      ...quiz,
      questions: quiz.questions.map(q => ({
        ...q,
        options: JSON.parse(q.options)
      }))
    }

    return NextResponse.json(
      { 
        message: 'Quiz created successfully', 
        quiz: transformedQuiz
      },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Quiz creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
