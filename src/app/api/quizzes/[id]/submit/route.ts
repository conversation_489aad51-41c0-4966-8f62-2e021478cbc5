import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { z } from 'zod'

const submitQuizSchema = z.object({
  answers: z.record(z.string(), z.string()), // questionId -> answer
})

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { answers } = submitQuizSchema.parse(body)

    // Get quiz with questions
    const quiz = await prisma.quiz.findUnique({
      where: { id: params.id },
      include: {
        questions: true,
        lesson: {
          include: {
            module: {
              include: {
                course: {
                  include: {
                    enrollments: {
                      where: {
                        userId: session.user.id
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!quiz) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 })
    }

    // Check if user is enrolled in the course
    if (quiz.lesson.module.course.enrollments.length === 0) {
      return NextResponse.json({ error: 'Not enrolled in this course' }, { status: 403 })
    }

    // Check if user has already submitted this quiz
    const existingSubmission = await prisma.submission.findFirst({
      where: {
        userId: session.user.id,
        quizId: params.id
      }
    })

    if (existingSubmission) {
      return NextResponse.json({ error: 'Quiz already submitted' }, { status: 400 })
    }

    // Calculate score
    let totalPoints = 0
    let earnedPoints = 0
    const results: any = {}

    quiz.questions.forEach(question => {
      totalPoints += question.points
      const userAnswer = answers[question.id]
      const isCorrect = userAnswer === question.correctAnswer
      
      if (isCorrect) {
        earnedPoints += question.points
      }

      results[question.id] = {
        userAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect,
        points: isCorrect ? question.points : 0
      }
    })

    const score = totalPoints > 0 ? (earnedPoints / totalPoints) * 100 : 0

    // Create submission
    const submission = await prisma.submission.create({
      data: {
        userId: session.user.id,
        quizId: params.id,
        answers: JSON.stringify(answers),
        score,
      }
    })

    // Update lesson progress if quiz passed (score >= 70%)
    if (score >= 70) {
      await prisma.progress.upsert({
        where: {
          userId_lessonId: {
            userId: session.user.id,
            lessonId: quiz.lessonId
          }
        },
        update: {
          isCompleted: true,
          completedAt: new Date(),
        },
        create: {
          userId: session.user.id,
          lessonId: quiz.lessonId,
          isCompleted: true,
          completedAt: new Date(),
        }
      })

      // Update course enrollment progress
      await updateCourseProgress(session.user.id, quiz.lesson.module.courseId)
    }

    return NextResponse.json({
      message: 'Quiz submitted successfully',
      submission: {
        ...submission,
        score,
        totalPoints,
        earnedPoints,
        results,
        passed: score >= 70
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Quiz submission error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function updateCourseProgress(userId: string, courseId: string) {
  // Get all lessons in the course
  const course = await prisma.course.findUnique({
    where: { id: courseId },
    include: {
      modules: {
        include: {
          lessons: {
            include: {
              progress: {
                where: { userId }
              }
            }
          }
        }
      }
    }
  })

  if (!course) return

  // Calculate progress
  const totalLessons = course.modules.reduce((acc, module) => acc + module.lessons.length, 0)
  const completedLessons = course.modules.reduce((acc, module) => 
    acc + module.lessons.filter(lesson => 
      lesson.progress.some(p => p.isCompleted)
    ).length, 0
  )

  const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0

  // Update enrollment progress
  await prisma.enrollment.updateMany({
    where: {
      userId,
      courseId
    },
    data: {
      progress: progressPercentage,
      completedAt: progressPercentage === 100 ? new Date() : null
    }
  })
}
