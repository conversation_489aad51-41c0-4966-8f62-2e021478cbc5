import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const courseId = params.id
    const { isPublished } = await request.json()

    // Update course publication status
    const course = await prisma.course.update({
      where: { id: courseId },
      data: { isPublished },
      include: {
        teacher: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json({ 
      course,
      message: `Course ${isPublished ? 'published' : 'unpublished'} successfully`
    })
  } catch (error) {
    console.error('Error updating course publication status:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const courseId = params.id

    // Check if course has enrollments
    const enrollmentCount = await prisma.enrollment.count({
      where: { courseId }
    })

    if (enrollmentCount > 0) {
      return NextResponse.json({ 
        error: 'Cannot delete course with active enrollments' 
      }, { status: 400 })
    }

    // Delete course and related data
    await prisma.$transaction(async (tx) => {
      // Delete lessons first
      await tx.lesson.deleteMany({
        where: {
          module: {
            courseId
          }
        }
      })

      // Delete modules
      await tx.module.deleteMany({
        where: { courseId }
      })

      // Delete discussions and replies
      await tx.reply.deleteMany({
        where: {
          discussion: {
            courseId
          }
        }
      })

      await tx.discussion.deleteMany({
        where: { courseId }
      })

      // Finally delete the course
      await tx.course.delete({
        where: { id: courseId }
      })
    })

    return NextResponse.json({ 
      message: 'Course deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting course:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
