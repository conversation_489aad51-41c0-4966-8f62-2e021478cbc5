import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'
    const category = searchParams.get('category') || 'all'

    // Build where clause
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { teacher: { name: { contains: search, mode: 'insensitive' } } }
      ]
    }

    if (status !== 'all') {
      where.isPublished = status === 'published'
    }

    if (category !== 'all') {
      where.category = category
    }

    const [courses, categories] = await Promise.all([
      prisma.course.findMany({
        where,
        include: {
          teacher: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          enrollments: {
            select: {
              id: true
            }
          },
          modules: {
            include: {
              lessons: {
                select: {
                  id: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.course.findMany({
        select: { category: true },
        distinct: ['category']
      })
    ])

    const coursesWithStats = courses.map(course => ({
      id: course.id,
      title: course.title,
      description: course.description,
      category: course.category,
      price: course.price,
      isPaid: course.isPaid,
      isPublished: course.isPublished,
      teacher: course.teacher,
      enrollmentCount: course.enrollments.length,
      lessonCount: course.modules.reduce((total, module) => total + module.lessons.length, 0)
    }))

    return NextResponse.json({ 
      courses: coursesWithStats,
      categories: categories.map(c => c.category)
    })
  } catch (error) {
    console.error('Error fetching courses:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
