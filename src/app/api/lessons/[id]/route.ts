import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { z } from 'zod'

const updateLessonSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  content: z.string().min(1, 'Content is required').optional(),
  videoUrl: z.string().url().optional().or(z.literal('')).optional(),
  attachments: z.array(z.string()).optional(),
  order: z.number().min(1).optional(),
  type: z.enum(['TEXT', 'VIDEO', 'QUIZ', 'ASSIGNMENT']).optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: lessonId } = await params

    // Get the lesson with all related data
    const lesson = await prisma.lesson.findUnique({
      where: { id: lessonId },
      include: {
        module: {
          include: {
            course: {
              select: {
                id: true,
                title: true,
                teacherId: true
              }
            },
            lessons: {
              select: {
                id: true,
                title: true,
                type: true,
                order: true
              },
              orderBy: { order: 'asc' }
            }
          }
        },
        quizzes: {
          include: {
            questions: {
              select: {
                id: true,
                points: true
              }
            }
          }
        }
      }
    })

    if (!lesson) {
      return NextResponse.json({ error: 'Lesson not found' }, { status: 404 })
    }

    // Check if user has access to this course
    const enrollment = await prisma.enrollment.findFirst({
      where: {
        userId: session.user.id,
        courseId: lesson.module.course.id
      }
    })

    const isTeacher = lesson.module.course.teacherId === session.user.id

    if (!enrollment && !isTeacher) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Get user's progress for this lesson
    const progress = await prisma.progress.findFirst({
      where: {
        userId: session.user.id,
        lessonId: lessonId
      }
    })

    // Parse attachments if they exist and format quiz data
    const formattedLesson = {
      ...lesson,
      attachments: lesson.attachments ? JSON.parse(lesson.attachments) : [],
      quiz: lesson.quizzes && lesson.quizzes.length > 0 ? lesson.quizzes[0] : null
    }

    return NextResponse.json({
      lesson: formattedLesson,
      progress: progress ? {
        isCompleted: progress.isCompleted,
        completedAt: progress.completedAt?.toISOString()
      } : { isCompleted: false }
    })
  } catch (error) {
    console.error('Error fetching lesson:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !['TEACHER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Get lesson with course info to check permissions
    const existingLesson = await prisma.lesson.findUnique({
      where: { id },
      include: {
        module: {
          include: {
            course: {
              select: {
                teacherId: true
              }
            }
          }
        }
      }
    })

    if (!existingLesson) {
      return NextResponse.json({ error: 'Lesson not found' }, { status: 404 })
    }

    // Check if user can edit this lesson
    const canEdit = session.user.role === 'ADMIN' ||
                   existingLesson.module.course.teacherId === session.user.id

    if (!canEdit) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const updateData = updateLessonSchema.parse(body)

    // Prepare update data
    const dataToUpdate: any = { ...updateData }
    if (updateData.attachments) {
      dataToUpdate.attachments = JSON.stringify(updateData.attachments)
    }
    if (updateData.videoUrl === '') {
      dataToUpdate.videoUrl = null
    }

    // Update lesson
    const lesson = await prisma.lesson.update({
      where: { id },
      data: dataToUpdate,
      include: {
        quizzes: true
      }
    })

    // Transform lesson to include parsed attachments
    const transformedLesson = {
      ...lesson,
      attachments: lesson.attachments ? JSON.parse(lesson.attachments) : []
    }

    return NextResponse.json({
      message: 'Lesson updated successfully',
      lesson: transformedLesson
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Lesson update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !['TEACHER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Get lesson with course info to check permissions
    const existingLesson = await prisma.lesson.findUnique({
      where: { id },
      include: {
        module: {
          include: {
            course: {
              select: {
                teacherId: true
              }
            }
          }
        }
      }
    })

    if (!existingLesson) {
      return NextResponse.json({ error: 'Lesson not found' }, { status: 404 })
    }

    // Check if user can delete this lesson
    const canDelete = session.user.role === 'ADMIN' ||
                     existingLesson.module.course.teacherId === session.user.id

    if (!canDelete) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Delete lesson
    await prisma.lesson.delete({
      where: { id }
    })

    return NextResponse.json({
      message: 'Lesson deleted successfully'
    })
  } catch (error) {
    console.error('Lesson deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
