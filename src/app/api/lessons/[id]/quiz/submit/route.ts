import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: lessonId } = await params
    const { answers } = await request.json()

    // Get the quiz for this lesson
    const quiz = await prisma.quiz.findFirst({
      where: { lessonId },
      include: {
        questions: {
          orderBy: { id: 'asc' }
        },
        lesson: {
          include: {
            module: {
              include: {
                course: {
                  select: {
                    id: true,
                    title: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!quiz) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 })
    }

    // Check if user has access to this course
    const enrollment = await prisma.enrollment.findFirst({
      where: {
        userId: session.user.id,
        courseId: quiz.lesson.module.course.id
      }
    })

    if (!enrollment) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Check if user has already submitted this quiz
    const existingSubmission = await prisma.submission.findFirst({
      where: {
        userId: session.user.id,
        quizId: quiz.id
      }
    })

    if (existingSubmission) {
      return NextResponse.json({ error: 'Quiz already submitted' }, { status: 400 })
    }

    // Calculate score
    let totalScore = 0
    let maxScore = 0

    for (const question of quiz.questions) {
      maxScore += question.points
      const userAnswer = answers[question.id]
      const correctAnswer = question.correctAnswer
      
      if (userAnswer && userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim()) {
        totalScore += question.points
      }
    }

    // Create submission
    const submission = await prisma.submission.create({
      data: {
        userId: session.user.id,
        quizId: quiz.id,
        answers: JSON.stringify(answers),
        score: totalScore
      }
    })

    // Update lesson progress if this is the first time completing the quiz
    const existingProgress = await prisma.progress.findFirst({
      where: {
        userId: session.user.id,
        lessonId: lessonId
      }
    })

    if (!existingProgress) {
      await prisma.progress.create({
        data: {
          userId: session.user.id,
          lessonId: lessonId,
          isCompleted: true,
          completedAt: new Date()
        }
      })
    } else if (!existingProgress.isCompleted) {
      await prisma.progress.update({
        where: { id: existingProgress.id },
        data: {
          isCompleted: true,
          completedAt: new Date()
        }
      })
    }

    return NextResponse.json({
      submission: {
        ...submission,
        answers: JSON.parse(submission.answers)
      },
      score: totalScore,
      maxScore,
      percentage: Math.round((totalScore / maxScore) * 100)
    })
  } catch (error) {
    console.error('Error submitting quiz:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
