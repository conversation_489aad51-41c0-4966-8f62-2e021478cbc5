import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: lessonId } = await params

    // Get the quiz for this lesson
    const quiz = await prisma.quiz.findFirst({
      where: { lessonId },
      include: {
        questions: {
          orderBy: { id: 'asc' }
        },
        lesson: {
          include: {
            module: {
              include: {
                course: {
                  select: {
                    id: true,
                    title: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!quiz) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 })
    }

    // Check if user has access to this course
    const enrollment = await prisma.enrollment.findFirst({
      where: {
        userId: session.user.id,
        courseId: quiz.lesson.module.course.id
      }
    })

    const isTeacher = quiz.lesson.module.course.id && await prisma.course.findFirst({
      where: {
        id: quiz.lesson.module.course.id,
        teacherId: session.user.id
      }
    })

    if (!enrollment && !isTeacher) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Check if user has already submitted this quiz
    const existingSubmission = await prisma.submission.findFirst({
      where: {
        userId: session.user.id,
        quizId: quiz.id
      }
    })

    // Format questions for frontend (parse JSON options)
    const formattedQuiz = {
      ...quiz,
      questions: quiz.questions.map(question => ({
        ...question,
        options: JSON.parse(question.options)
      }))
    }

    return NextResponse.json({
      quiz: formattedQuiz,
      submission: existingSubmission ? {
        ...existingSubmission,
        answers: JSON.parse(existingSubmission.answers)
      } : null
    })
  } catch (error) {
    console.error('Error fetching quiz:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
