import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: lessonId } = await params

    // Get the lesson to verify access
    const lesson = await prisma.lesson.findUnique({
      where: { id: lessonId },
      include: {
        module: {
          include: {
            course: {
              select: {
                id: true
              }
            }
          }
        }
      }
    })

    if (!lesson) {
      return NextResponse.json({ error: 'Lesson not found' }, { status: 404 })
    }

    // Check if user has access to this course
    const enrollment = await prisma.enrollment.findFirst({
      where: {
        userId: session.user.id,
        courseId: lesson.module.course.id
      }
    })

    if (!enrollment) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Check if progress already exists
    const existingProgress = await prisma.progress.findFirst({
      where: {
        userId: session.user.id,
        lessonId: lessonId
      }
    })

    if (existingProgress) {
      // Update existing progress
      const updatedProgress = await prisma.progress.update({
        where: { id: existingProgress.id },
        data: {
          isCompleted: true,
          completedAt: new Date()
        }
      })
      
      return NextResponse.json({
        progress: {
          isCompleted: updatedProgress.isCompleted,
          completedAt: updatedProgress.completedAt?.toISOString()
        }
      })
    } else {
      // Create new progress record
      const newProgress = await prisma.progress.create({
        data: {
          userId: session.user.id,
          lessonId: lessonId,
          isCompleted: true,
          completedAt: new Date()
        }
      })
      
      return NextResponse.json({
        progress: {
          isCompleted: newProgress.isCompleted,
          completedAt: newProgress.completedAt?.toISOString()
        }
      })
    }
  } catch (error) {
    console.error('Error marking lesson as complete:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
