import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { z } from 'zod'

const updateProgressSchema = z.object({
  isCompleted: z.boolean().optional(),
  timeSpent: z.number().min(0).optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const progress = await prisma.progress.findUnique({
      where: {
        userId_lessonId: {
          userId: session.user.id,
          lessonId: params.id
        }
      },
      include: {
        lesson: {
          include: {
            module: {
              include: {
                course: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({ progress })
  } catch (error) {
    console.error('Progress fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify lesson exists and user is enrolled
    const lesson = await prisma.lesson.findUnique({
      where: { id: params.id },
      include: {
        module: {
          include: {
            course: {
              include: {
                enrollments: {
                  where: {
                    userId: session.user.id
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!lesson) {
      return NextResponse.json({ error: 'Lesson not found' }, { status: 404 })
    }

    if (lesson.module.course.enrollments.length === 0) {
      return NextResponse.json({ error: 'Not enrolled in this course' }, { status: 403 })
    }

    const body = await request.json()
    const { isCompleted, timeSpent } = updateProgressSchema.parse(body)

    // Update or create progress
    const progress = await prisma.progress.upsert({
      where: {
        userId_lessonId: {
          userId: session.user.id,
          lessonId: params.id
        }
      },
      update: {
        ...(isCompleted !== undefined && { 
          isCompleted,
          completedAt: isCompleted ? new Date() : null
        }),
        ...(timeSpent !== undefined && { 
          timeSpent: { increment: timeSpent }
        })
      },
      create: {
        userId: session.user.id,
        lessonId: params.id,
        isCompleted: isCompleted || false,
        completedAt: isCompleted ? new Date() : null,
        timeSpent: timeSpent || 0
      }
    })

    // Update course enrollment progress if lesson was completed
    if (isCompleted) {
      await updateCourseProgress(session.user.id, lesson.module.courseId)
    }

    return NextResponse.json({
      message: 'Progress updated successfully',
      progress
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Progress update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function updateCourseProgress(userId: string, courseId: string) {
  // Get all lessons in the course
  const course = await prisma.course.findUnique({
    where: { id: courseId },
    include: {
      modules: {
        include: {
          lessons: {
            include: {
              progress: {
                where: { userId }
              }
            }
          }
        }
      }
    }
  })

  if (!course) return

  // Calculate progress
  const totalLessons = course.modules.reduce((acc, module) => acc + module.lessons.length, 0)
  const completedLessons = course.modules.reduce((acc, module) => 
    acc + module.lessons.filter(lesson => 
      lesson.progress.some(p => p.isCompleted)
    ).length, 0
  )

  const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0

  // Update enrollment progress
  await prisma.enrollment.updateMany({
    where: {
      userId,
      courseId
    },
    data: {
      progress: progressPercentage,
      completedAt: progressPercentage === 100 ? new Date() : null
    }
  })
}
