import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { z } from 'zod'

const updateCourseSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  description: z.string().min(1, 'Description is required').optional(),
  category: z.string().min(1, 'Category is required').optional(),
  tags: z.array(z.string()).optional(),
  price: z.number().min(0).optional(),
  isPaid: z.boolean().optional(),
  isPublished: z.boolean().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const course = await prisma.course.findUnique({
      where: { id },
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        },
        modules: {
          include: {
            lessons: {
              orderBy: { order: 'asc' }
            }
          },
          orderBy: { order: 'asc' }
        },
        enrollments: {
          select: {
            id: true,
            userId: true,
            progress: true,
            enrolledAt: true,
            completedAt: true,
          }
        },
        discussions: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                avatar: true,
              }
            },
            replies: {
              include: {
                author: {
                  select: {
                    id: true,
                    name: true,
                    avatar: true,
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!course) {
      return NextResponse.json({ error: 'Course not found' }, { status: 404 })
    }

    // Transform course data
    const transformedCourse = {
      ...course,
      tags: course.tags ? JSON.parse(course.tags) : [],
      enrollmentCount: course.enrollments.length,
      lessonCount: course.modules.reduce((acc, module) => acc + module.lessons.length, 0),
    }

    return NextResponse.json({ course: transformedCourse })
  } catch (error) {
    console.error('Course fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    // Get the course to check ownership
    const existingCourse = await prisma.course.findUnique({
      where: { id }
    })

    if (!existingCourse) {
      return NextResponse.json({ error: 'Course not found' }, { status: 404 })
    }

    // Check if user can edit this course
    const canEdit = session.user.role === 'ADMIN' || 
                   (session.user.role === 'TEACHER' && existingCourse.teacherId === session.user.id)

    if (!canEdit) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const updateData = updateCourseSchema.parse(body)

    // Transform tags to JSON string if provided
    const finalUpdateData: any = { ...updateData }
    if (updateData.tags) {
      finalUpdateData.tags = JSON.stringify(updateData.tags)
    }

    // Update course
    const updatedCourse = await prisma.course.update({
      where: { id },
      data: finalUpdateData,
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Course updated successfully',
      course: {
        ...updatedCourse,
        tags: updatedCourse.tags ? JSON.parse(updatedCourse.tags) : []
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Course update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    // Get the course to check ownership
    const existingCourse = await prisma.course.findUnique({
      where: { id }
    })

    if (!existingCourse) {
      return NextResponse.json({ error: 'Course not found' }, { status: 404 })
    }

    // Check if user can delete this course
    const canDelete = session.user.role === 'ADMIN' || 
                     (session.user.role === 'TEACHER' && existingCourse.teacherId === session.user.id)

    if (!canDelete) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Delete course (cascade will handle related records)
    await prisma.course.delete({
      where: { id }
    })

    return NextResponse.json({ message: 'Course deleted successfully' })
  } catch (error) {
    console.error('Course deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
