import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get course and enrollment details
    const enrollment = await prisma.enrollment.findUnique({
      where: {
        userId_courseId: {
          userId: session.user.id,
          courseId: params.id
        }
      },
      include: {
        course: {
          include: {
            teacher: {
              select: {
                name: true
              }
            }
          }
        },
        user: {
          select: {
            name: true
          }
        }
      }
    })

    if (!enrollment) {
      return NextResponse.json({ error: 'Not enrolled in this course' }, { status: 404 })
    }

    if (!enrollment.completedAt || enrollment.progress < 100) {
      return NextResponse.json({ error: 'Course not completed' }, { status: 400 })
    }

    // Generate certificate data
    const certificate = {
      id: `CERT-${enrollment.id}`,
      studentName: enrollment.user.name,
      courseName: enrollment.course.title,
      instructorName: enrollment.course.teacher.name,
      completionDate: enrollment.completedAt,
      enrollmentDate: enrollment.enrolledAt,
      certificateUrl: `/certificates/${enrollment.id}`,
    }

    return NextResponse.json({ certificate })
  } catch (error) {
    console.error('Certificate generation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
