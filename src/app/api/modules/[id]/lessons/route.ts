import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { z } from 'zod'

const createLessonSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  videoUrl: z.string().url().optional().or(z.literal('')),
  attachments: z.array(z.string()).default([]),
  order: z.number().min(1),
  type: z.enum(['TEXT', 'VIDEO', 'QUIZ', 'ASSIGNMENT']).default('TEXT'),
})

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const lessons = await prisma.lesson.findMany({
      where: { moduleId: id },
      include: {
        quizzes: true,
        progress: true
      },
      orderBy: { order: 'asc' }
    })

    // Transform lessons to include parsed attachments
    const transformedLessons = lessons.map(lesson => ({
      ...lesson,
      attachments: lesson.attachments ? JSON.parse(lesson.attachments) : []
    }))

    return NextResponse.json({ lessons: transformedLessons })
  } catch (error) {
    console.error('Lessons fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || !['TEACHER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Verify module exists and user has permission
    const module = await prisma.module.findUnique({
      where: { id },
      include: {
        course: true
      }
    })

    if (!module) {
      return NextResponse.json({ error: 'Module not found' }, { status: 404 })
    }

    const canEdit = session.user.role === 'ADMIN' || module.course.teacherId === session.user.id

    if (!canEdit) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const { title, content, videoUrl, attachments, order, type } = createLessonSchema.parse(body)

    // Create lesson
    const lesson = await prisma.lesson.create({
      data: {
        title,
        content,
        videoUrl: videoUrl || null,
        attachments: JSON.stringify(attachments),
        order,
        type,
        moduleId: id,
      },
      include: {
        quizzes: true
      }
    })

    // Transform lesson to include parsed attachments
    const transformedLesson = {
      ...lesson,
      attachments: JSON.parse(lesson.attachments)
    }

    return NextResponse.json(
      { message: 'Lesson created successfully', lesson: transformedLesson },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Lesson creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
