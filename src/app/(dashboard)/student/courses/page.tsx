import { requireStudent } from '@/lib/auth-utils'
import { prisma } from '@/lib/db'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Navbar from '@/components/navigation/navbar'
import Link from 'next/link'
import { BookOpen, Clock, Award, TrendingUp, Play, MessageSquare } from 'lucide-react'

export default async function StudentCoursesPage() {
  const session = await requireStudent()
  
  // Get student's enrollments with detailed course information
  const enrollments = await prisma.enrollment.findMany({
    where: { userId: session.user.id },
    include: {
      course: {
        include: {
          teacher: {
            select: {
              id: true,
              name: true,
              avatar: true,
            }
          },
          modules: {
            include: {
              lessons: {
                include: {
                  progress: {
                    where: {
                      userId: session.user.id
                    }
                  }
                }
              }
            },
            orderBy: { order: 'asc' }
          }
        }
      }
    },
    orderBy: { enrolledAt: 'desc' }
  })

  // Calculate detailed progress for each course
  const coursesWithProgress = enrollments.map(enrollment => {
    const course = enrollment.course
    const totalLessons = course.modules.reduce((acc, module) => acc + module.lessons.length, 0)
    const completedLessons = course.modules.reduce((acc, module) => 
      acc + module.lessons.filter(lesson => 
        lesson.progress.some(p => p.isCompleted)
      ).length, 0
    )
    
    const progressPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0
    
    // Find next lesson to continue
    let nextLesson = null
    for (const module of course.modules) {
      for (const lesson of module.lessons) {
        const hasProgress = lesson.progress.some(p => p.isCompleted)
        if (!hasProgress) {
          nextLesson = { lesson, module }
          break
        }
      }
      if (nextLesson) break
    }

    return {
      ...enrollment,
      course: {
        ...course,
        totalLessons,
        completedLessons,
        progressPercentage,
        nextLesson
      }
    }
  })

  const completedCourses = coursesWithProgress.filter(e => e.course.progressPercentage === 100)
  const inProgressCourses = coursesWithProgress.filter(e => e.course.progressPercentage > 0 && e.course.progressPercentage < 100)
  const notStartedCourses = coursesWithProgress.filter(e => e.course.progressPercentage === 0)

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">My Courses</h1>
          <p className="mt-2 text-gray-600">
            Track your learning progress and continue where you left off.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{coursesWithProgress.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Progress</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{inProgressCourses.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{completedCourses.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Progress</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {coursesWithProgress.length > 0 
                  ? Math.round(coursesWithProgress.reduce((acc, e) => acc + e.course.progressPercentage, 0) / coursesWithProgress.length)
                  : 0}%
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Continue Learning Section */}
        {inProgressCourses.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Continue Learning</CardTitle>
              <CardDescription>
                Pick up where you left off
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {inProgressCourses.slice(0, 2).map((enrollment) => (
                  <div key={enrollment.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="font-medium text-lg">{enrollment.course.title}</h3>
                        <p className="text-sm text-gray-600">by {enrollment.course.teacher.name}</p>
                      </div>
                      <span className="text-sm font-medium text-indigo-600">
                        {enrollment.course.progressPercentage}%
                      </span>
                    </div>
                    
                    <div className="mb-3">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-indigo-600 h-2 rounded-full" 
                          style={{ width: `${enrollment.course.progressPercentage}%` }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {enrollment.course.completedLessons} of {enrollment.course.totalLessons} lessons completed
                      </p>
                    </div>

                    {enrollment.course.nextLesson && (
                      <div className="mb-3">
                        <p className="text-sm text-gray-600">
                          Next: {enrollment.course.nextLesson.lesson.title}
                        </p>
                      </div>
                    )}

                    <div className="space-y-2">
                      <Link href={`/courses/${enrollment.course.id}`}>
                        <Button size="sm" className="w-full">
                          <Play className="h-4 w-4 mr-2" />
                          Continue Learning
                        </Button>
                      </Link>
                      <Link href={`/courses/${enrollment.course.id}/discussions`}>
                        <Button variant="outline" size="sm" className="w-full">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Discussions
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* All Courses */}
        <div className="space-y-6">
          {/* In Progress */}
          {inProgressCourses.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>In Progress ({inProgressCourses.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {inProgressCourses.map((enrollment) => (
                    <CourseCard key={enrollment.id} enrollment={enrollment} />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Not Started */}
          {notStartedCourses.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Not Started ({notStartedCourses.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {notStartedCourses.map((enrollment) => (
                    <CourseCard key={enrollment.id} enrollment={enrollment} />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Completed */}
          {completedCourses.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Completed ({completedCourses.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {completedCourses.map((enrollment) => (
                    <CourseCard key={enrollment.id} enrollment={enrollment} />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {coursesWithProgress.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <BookOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No courses yet</h3>
              <p className="text-gray-600 mb-6">
                Start your learning journey by browsing our course catalog.
              </p>
              <Link href="/courses">
                <Button>Browse Courses</Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

function CourseCard({ enrollment }: { enrollment: any }) {
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex-1">
        <h3 className="font-medium">{enrollment.course.title}</h3>
        <p className="text-sm text-gray-600">by {enrollment.course.teacher.name}</p>
        <div className="mt-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-indigo-600 h-2 rounded-full" 
              style={{ width: `${enrollment.course.progressPercentage}%` }}
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {enrollment.course.completedLessons} of {enrollment.course.totalLessons} lessons completed
          </p>
        </div>
      </div>
      <div className="flex items-center space-x-3">
        <span className="text-sm font-medium">
          {enrollment.course.progressPercentage}%
        </span>
        <div className="flex space-x-2">
          <Link href={`/courses/${enrollment.course.id}`}>
            <Button size="sm">
              {enrollment.course.progressPercentage === 0 ? 'Start' : 'Continue'}
            </Button>
          </Link>
          <Link href={`/courses/${enrollment.course.id}/discussions`}>
            <Button variant="outline" size="sm">
              <MessageSquare className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
