import { requireStudent } from '@/lib/auth-utils'
import { prisma } from '@/lib/db'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Navbar from '@/components/navigation/navbar'
import Link from 'next/link'
import { BookOpen, Clock, Award, TrendingUp } from 'lucide-react'

export default async function StudentDashboard() {
  const session = await requireStudent()
  
  // Get student's enrollments and progress
  const enrollments = await prisma.enrollment.findMany({
    where: { userId: session.user.id },
    include: {
      course: {
        include: {
          teacher: true,
          modules: {
            include: {
              lessons: true
            }
          }
        }
      }
    }
  })

  // Get recent progress
  const recentProgress = await prisma.progress.findMany({
    where: { userId: session.user.id },
    include: {
      lesson: {
        include: {
          module: {
            include: {
              course: true
            }
          }
        }
      }
    },
    orderBy: { completedAt: 'desc' },
    take: 5
  })

  // Calculate stats
  const totalCourses = enrollments.length
  const completedCourses = enrollments.filter(e => e.completedAt).length
  const totalLessons = enrollments.reduce((acc, e) => 
    acc + e.course.modules.reduce((modAcc, m) => modAcc + m.lessons.length, 0), 0
  )
  const completedLessons = recentProgress.filter(p => p.isCompleted).length

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {session.user.name}!
          </h1>
          <p className="mt-2 text-gray-600">
            Continue your learning journey and track your progress.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Enrolled Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalCourses}</div>
              <p className="text-xs text-muted-foreground">
                {completedCourses} completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Lessons Completed</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{completedLessons}</div>
              <p className="text-xs text-muted-foreground">
                of {totalLessons} total lessons
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Study Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(recentProgress.reduce((acc, p) => acc + p.timeSpent, 0) / 60)}h
              </div>
              <p className="text-xs text-muted-foreground">
                Total time spent
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Progress</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0}%
              </div>
              <p className="text-xs text-muted-foreground">
                Overall completion
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Current Courses */}
          <Card>
            <CardHeader>
              <CardTitle>My Courses</CardTitle>
              <CardDescription>
                Continue learning from where you left off
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {enrollments.length > 0 ? (
                  enrollments.slice(0, 3).map((enrollment) => (
                    <div key={enrollment.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <h3 className="font-medium">{enrollment.course.title}</h3>
                        <p className="text-sm text-gray-600">
                          by {enrollment.course.teacher.name}
                        </p>
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-indigo-600 h-2 rounded-full" 
                              style={{ width: `${enrollment.progress}%` }}
                            />
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {enrollment.progress}% complete
                          </p>
                        </div>
                      </div>
                      <Link href={`/courses/${enrollment.course.id}`}>
                        <Button size="sm">Continue</Button>
                      </Link>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No courses yet</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Start learning by browsing our course catalog.
                    </p>
                    <div className="mt-6">
                      <Link href="/courses">
                        <Button>Browse Courses</Button>
                      </Link>
                    </div>
                  </div>
                )}
              </div>
              {enrollments.length > 3 && (
                <div className="mt-4 text-center">
                  <Link href="/student/courses">
                    <Button variant="outline">View All Courses</Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Your latest learning progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentProgress.length > 0 ? (
                  recentProgress.map((progress) => (
                    <div key={progress.id} className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        {progress.isCompleted ? (
                          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <Award className="w-4 h-4 text-green-600" />
                          </div>
                        ) : (
                          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <Clock className="w-4 h-4 text-gray-600" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {progress.lesson.title}
                        </p>
                        <p className="text-sm text-gray-500 truncate">
                          {progress.lesson.module.course.title}
                        </p>
                      </div>
                      <div className="text-sm text-gray-500">
                        {progress.completedAt ? 'Completed' : 'In Progress'}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Clock className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No activity yet</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Start a course to see your progress here.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
