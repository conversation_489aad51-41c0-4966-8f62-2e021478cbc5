import { requireAdmin } from '@/lib/auth-utils'
import { prisma } from '@/lib/db'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import Navbar from '@/components/navigation/navbar'
import {
  Users,
  BookOpen,
  TrendingUp,
  Activity,
  Target
} from 'lucide-react'

export default async function AdminAnalyticsPage() {
  await requireAdmin()

  // Get comprehensive analytics data
  const [
    totalUsers,
    totalCourses,
    totalEnrollments,
    completedCourses,
    usersByRole,
    coursesByCategory,
    enrollmentsByMonth,
    topCourses,
    recentActivity,
    completionRates
  ] = await Promise.all([
    // Basic counts
    prisma.user.count(),
    prisma.course.count(),
    prisma.enrollment.count(),
    prisma.enrollment.count({
      where: { completedAt: { not: null } }
    }),
    
    // User distribution by role
    prisma.user.groupBy({
      by: ['role'],
      _count: { role: true }
    }),
    
    // Course distribution by category
    prisma.course.groupBy({
      by: ['category'],
      _count: { category: true },
      where: { isPublished: true }
    }),
    
    // Enrollments by month (last 6 months)
    prisma.enrollment.findMany({
      where: {
        enrolledAt: {
          gte: new Date(new Date().setMonth(new Date().getMonth() - 6))
        }
      },
      select: {
        enrolledAt: true
      }
    }),
    
    // Top courses by enrollment
    prisma.course.findMany({
      where: { isPublished: true },
      include: {
        enrollments: {
          select: { id: true }
        },
        teacher: {
          select: { name: true }
        }
      },
      orderBy: {
        enrollments: {
          _count: 'desc'
        }
      },
      take: 5
    }),
    
    // Recent activity (enrollments)
    prisma.enrollment.findMany({
      orderBy: { enrolledAt: 'desc' },
      take: 10,
      include: {
        user: {
          select: { name: true, email: true }
        },
        course: {
          select: { title: true }
        }
      }
    }),
    
    // Course completion rates
    prisma.course.findMany({
      where: { isPublished: true },
      include: {
        enrollments: {
          select: {
            id: true,
            completedAt: true
          }
        }
      },
      take: 10
    })
  ])

  // Process data for charts
  const roleStats = usersByRole.reduce((acc, item) => {
    acc[item.role] = item._count.role
    return acc
  }, {} as Record<string, number>)

  const categoryStats = coursesByCategory.reduce((acc, item) => {
    acc[item.category] = item._count.category
    return acc
  }, {} as Record<string, number>)



  // Calculate completion rates
  const coursesWithCompletion = completionRates.map(course => ({
    ...course,
    enrollmentCount: course.enrollments.length,
    completionCount: course.enrollments.filter(e => e.completedAt).length,
    completionRate: course.enrollments.length > 0 
      ? Math.round((course.enrollments.filter(e => e.completedAt).length / course.enrollments.length) * 100)
      : 0
  }))

  const avgCompletionRate = coursesWithCompletion.length > 0
    ? Math.round(coursesWithCompletion.reduce((sum, course) => sum + course.completionRate, 0) / coursesWithCompletion.length)
    : 0

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="mt-2 text-gray-600">
            Comprehensive platform analytics and insights.
          </p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalUsers}</div>
              <p className="text-xs text-gray-600">
                {roleStats.STUDENT || 0} students, {roleStats.TEACHER || 0} teachers
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Enrollments</CardTitle>
              <TrendingUp className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalEnrollments}</div>
              <p className="text-xs text-gray-600">
                {completedCourses} completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
              <Target className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{avgCompletionRate}%</div>
              <p className="text-xs text-gray-600">
                Average across all courses
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalCourses}</div>
              <p className="text-xs text-gray-600">
                Published and available
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* User Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>User Distribution</CardTitle>
              <CardDescription>Users by role</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(roleStats).map(([role, count]) => (
                  <div key={role} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${
                        role === 'STUDENT' ? 'bg-blue-500' :
                        role === 'TEACHER' ? 'bg-green-500' : 'bg-purple-500'
                      }`} />
                      <span className="text-sm font-medium">{role}</span>
                    </div>
                    <span className="text-sm text-gray-600">{count}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Course Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Course Categories</CardTitle>
              <CardDescription>Published courses by category</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(categoryStats).map(([category, count]) => (
                  <div key={category} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{category}</span>
                    <span className="text-sm text-gray-600">{count} courses</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Top Courses */}
          <Card>
            <CardHeader>
              <CardTitle>Top Courses</CardTitle>
              <CardDescription>Most enrolled courses</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topCourses.map((course, index) => (
                  <div key={course.id} className="flex items-center space-x-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-indigo-600">
                        {index + 1}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {course.title}
                      </p>
                      <p className="text-xs text-gray-500">
                        by {course.teacher.name}
                      </p>
                    </div>
                    <div className="text-sm text-gray-600">
                      {course.enrollments.length} students
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Enrollments</CardTitle>
              <CardDescription>Latest student enrollments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((enrollment) => (
                  <div key={enrollment.id} className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <Activity className="h-5 w-5 text-green-500" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {enrollment.user.name}
                      </p>
                      <p className="text-xs text-gray-500 truncate">
                        enrolled in {enrollment.course.title}
                      </p>
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(enrollment.enrolledAt).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
