import { requireTeacher } from '@/lib/auth-utils'
import { prisma } from '@/lib/db'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Navbar from '@/components/navigation/navbar'
import Link from 'next/link'
import { BookOpen, Users, TrendingUp, Plus, Eye, Edit, Settings } from 'lucide-react'

export default async function TeacherCoursesPage() {
  const session = await requireTeacher()
  
  // Get teacher's courses with detailed information
  const courses = await prisma.course.findMany({
    where: { teacherId: session.user.id },
    include: {
      enrollments: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        }
      },
      modules: {
        include: {
          lessons: {
            include: {
              progress: {
                select: {
                  id: true,
                  isCompleted: true,
                  userId: true,
                }
              }
            }
          }
        },
        orderBy: { order: 'asc' }
      }
    },
    orderBy: { createdAt: 'desc' }
  })

  // Calculate detailed stats for each course
  const coursesWithStats = courses.map(course => {
    const totalLessons = course.modules.reduce((acc, module) => acc + module.lessons.length, 0)
    const totalStudents = course.enrollments.length
    
    // Calculate average progress
    let totalProgress = 0
    course.enrollments.forEach(enrollment => {
      const studentProgress = course.modules.reduce((acc, module) => {
        const completedLessons = module.lessons.filter(lesson =>
          lesson.progress.some(p => p.userId === enrollment.userId && p.isCompleted)
        ).length
        return acc + completedLessons
      }, 0)
      totalProgress += totalLessons > 0 ? (studentProgress / totalLessons) * 100 : 0
    })
    
    const averageProgress = totalStudents > 0 ? Math.round(totalProgress / totalStudents) : 0

    return {
      ...course,
      totalLessons,
      totalStudents,
      averageProgress,
    }
  })

  const publishedCourses = coursesWithStats.filter(c => c.isPublished)
  const draftCourses = coursesWithStats.filter(c => !c.isPublished)

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Courses</h1>
            <p className="mt-2 text-gray-600">
              Manage your courses and track student progress.
            </p>
          </div>
          <Link href="/courses/create">
            <Button className="flex items-center space-x-2">
              <Plus className="h-4 w-4" />
              <span>Create Course</span>
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{coursesWithStats.length}</div>
              <p className="text-xs text-muted-foreground">
                {publishedCourses.length} published
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {coursesWithStats.reduce((acc, course) => acc + course.totalStudents, 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Across all courses
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Progress</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {coursesWithStats.length > 0 
                  ? Math.round(coursesWithStats.reduce((acc, course) => acc + course.averageProgress, 0) / coursesWithStats.length)
                  : 0}%
              </div>
              <p className="text-xs text-muted-foreground">
                Student completion
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${coursesWithStats.reduce((acc, course) => 
                  acc + (course.isPaid ? course.price * course.totalStudents : 0), 0
                ).toFixed(2)}
              </div>
              <p className="text-xs text-muted-foreground">
                From paid courses
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Course Sections */}
        <div className="space-y-8">
          {/* Published Courses */}
          {publishedCourses.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Published Courses ({publishedCourses.length})</CardTitle>
                <CardDescription>
                  Your live courses that students can enroll in
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {publishedCourses.map((course) => (
                    <CourseCard key={course.id} course={course} />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Draft Courses */}
          {draftCourses.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Draft Courses ({draftCourses.length})</CardTitle>
                <CardDescription>
                  Courses that are not yet published
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {draftCourses.map((course) => (
                    <CourseCard key={course.id} course={course} />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {coursesWithStats.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <BookOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No courses yet</h3>
              <p className="text-gray-600 mb-6">
                Create your first course to start teaching and sharing your knowledge.
              </p>
              <Link href="/courses/create">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Course
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

function CourseCard({ course }: { course: any }) {
  return (
    <div className="border rounded-lg p-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <h3 className="text-lg font-medium">{course.title}</h3>
            <span className={`px-2 py-1 text-xs rounded-full ${
              course.isPublished 
                ? 'bg-green-100 text-green-800' 
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {course.isPublished ? 'Published' : 'Draft'}
            </span>
          </div>
          <p className="text-gray-600 mb-3">{course.description}</p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Students:</span>
              <span className="ml-1 font-medium">{course.totalStudents}</span>
            </div>
            <div>
              <span className="text-gray-500">Lessons:</span>
              <span className="ml-1 font-medium">{course.totalLessons}</span>
            </div>
            <div>
              <span className="text-gray-500">Progress:</span>
              <span className="ml-1 font-medium">{course.averageProgress}%</span>
            </div>
            <div>
              <span className="text-gray-500">Price:</span>
              <span className="ml-1 font-medium">
                {course.isPaid ? `$${course.price}` : 'Free'}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-500">
          Created {new Date(course.createdAt).toLocaleDateString()}
        </div>
        
        <div className="flex items-center space-x-2">
          <Link href={`/courses/${course.id}`}>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-1" />
              View
            </Button>
          </Link>
          <Link href={`/teacher/courses/${course.id}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          </Link>
          <Link href={`/teacher/courses/${course.id}/analytics`}>
            <Button variant="outline" size="sm">
              <TrendingUp className="h-4 w-4 mr-1" />
              Analytics
            </Button>
          </Link>
          <Link href={`/teacher/courses/${course.id}/settings`}>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-1" />
              Settings
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
