'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Navbar from '@/components/navigation/navbar'
import { Loading } from '@/components/ui/loading'
import { 
  ArrowLeft, 
  Save, 
  Trash2, 
  Globe, 
  Lock, 
  Upload,
  AlertTriangle,
  Settings,
  Eye,
  EyeOff
} from 'lucide-react'
import Link from 'next/link'

interface Course {
  id: string
  title: string
  description: string
  category: string
  tags: string[]
  price: number
  isPaid: boolean
  isPublished: boolean
  thumbnail?: string
}

export default function CourseSettingsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const courseId = params.id as string

  const [course, setCourse] = useState<Course | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [deleting, setDeleting] = useState(false)
  const [error, setError] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  useEffect(() => {
    if (courseId) {
      fetchCourse()
    }
  }, [courseId])

  const fetchCourse = async () => {
    try {
      const response = await fetch(`/api/courses/${courseId}`)
      if (response.ok) {
        const data = await response.json()
        setCourse(data.course)
      } else {
        setError('Failed to load course')
      }
    } catch (error) {
      setError('An error occurred while loading the course')
    } finally {
      setLoading(false)
    }
  }

  const togglePublishStatus = async () => {
    if (!course) return

    setSaving(true)
    setError('')

    try {
      const response = await fetch(`/api/courses/${courseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isPublished: !course.isPublished,
        }),
      })

      const result = await response.json()

      if (response.ok) {
        setCourse(result.course)
      } else {
        setError(result.error || 'Failed to update course')
      }
    } catch (error) {
      setError('An error occurred while updating')
    } finally {
      setSaving(false)
    }
  }

  const deleteCourse = async () => {
    setDeleting(true)
    setError('')

    try {
      const response = await fetch(`/api/courses/${courseId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        router.push('/teacher/courses')
      } else {
        const result = await response.json()
        setError(result.error || 'Failed to delete course')
      }
    } catch (error) {
      setError('An error occurred while deleting')
    } finally {
      setDeleting(false)
      setShowDeleteConfirm(false)
    }
  }

  if (status === 'loading' || loading) {
    return <Loading />
  }

  if (!session || !['TEACHER', 'ADMIN'].includes(session.user.role)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access course settings.</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Course Not Found</h1>
          <p className="text-gray-600">The course you're looking for doesn't exist.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/teacher/courses" className="flex items-center text-blue-600 hover:text-blue-500 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to My Courses
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Course Settings</h1>
              <p className="mt-2 text-gray-600">
                {course.title}
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <Link href={`/teacher/courses/${course.id}/edit`}>
                <Button variant="outline">
                  Edit Content
                </Button>
              </Link>
              <Link href={`/courses/${course.id}`}>
                <Button variant="outline">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <div className="space-y-6">
          {/* Publishing Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                Publishing Settings
              </CardTitle>
              <CardDescription>
                Control the visibility and availability of your course
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${
                    course.isPublished ? 'bg-green-100' : 'bg-yellow-100'
                  }`}>
                    {course.isPublished ? (
                      <Globe className="h-5 w-5 text-green-600" />
                    ) : (
                      <Lock className="h-5 w-5 text-yellow-600" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">
                      {course.isPublished ? 'Published' : 'Draft'}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {course.isPublished 
                        ? 'Your course is live and visible to students'
                        : 'Your course is in draft mode and not visible to students'
                      }
                    </p>
                  </div>
                </div>
                <Button 
                  onClick={togglePublishStatus}
                  disabled={saving}
                  variant={course.isPublished ? 'outline' : 'default'}
                >
                  {saving ? (
                    <>
                      <Loading size="sm" className="mr-2" />
                      {course.isPublished ? 'Unpublishing...' : 'Publishing...'}
                    </>
                  ) : (
                    <>
                      {course.isPublished ? (
                        <>
                          <EyeOff className="h-4 w-4 mr-2" />
                          Unpublish
                        </>
                      ) : (
                        <>
                          <Globe className="h-4 w-4 mr-2" />
                          Publish
                        </>
                      )}
                    </>
                  )}
                </Button>
              </div>

              {!course.isPublished && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-yellow-800">Course is in Draft Mode</h4>
                      <p className="text-sm text-yellow-700 mt-1">
                        Students cannot see or enroll in your course while it's in draft mode. 
                        Make sure to add content and publish when ready.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Course Information */}
          <Card>
            <CardHeader>
              <CardTitle>Course Information</CardTitle>
              <CardDescription>
                Basic details about your course
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Course ID
                  </label>
                  <Input value={course.id} disabled />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category
                  </label>
                  <Input value={course.category} disabled />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tags
                </label>
                <Input value={course.tags.join(', ')} disabled />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Course Type
                  </label>
                  <Input value={course.isPaid ? 'Paid' : 'Free'} disabled />
                </div>
                {course.isPaid && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price
                    </label>
                    <Input value={`$${course.price.toFixed(2)}`} disabled />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Danger Zone */}
          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="text-red-900">Danger Zone</CardTitle>
              <CardDescription>
                Irreversible actions for this course
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border border-red-200 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-red-900 mb-2">Delete Course</h3>
                  <p className="text-sm text-red-700 mb-4">
                    Once you delete a course, there is no going back. This will permanently delete 
                    the course, all its content, and remove all student enrollments.
                  </p>
                  
                  {!showDeleteConfirm ? (
                    <Button 
                      variant="destructive"
                      onClick={() => setShowDeleteConfirm(true)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Course
                    </Button>
                  ) : (
                    <div className="space-y-3">
                      <p className="text-sm font-medium text-red-900">
                        Are you absolutely sure? This action cannot be undone.
                      </p>
                      <div className="flex space-x-3">
                        <Button 
                          variant="destructive"
                          onClick={deleteCourse}
                          disabled={deleting}
                        >
                          {deleting ? (
                            <>
                              <Loading size="sm" className="mr-2" />
                              Deleting...
                            </>
                          ) : (
                            <>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Yes, Delete Forever
                            </>
                          )}
                        </Button>
                        <Button 
                          variant="outline"
                          onClick={() => setShowDeleteConfirm(false)}
                          disabled={deleting}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
