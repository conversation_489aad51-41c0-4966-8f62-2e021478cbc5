import { requireTeacher } from '@/lib/auth-utils'
import { prisma } from '@/lib/db'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Navbar from '@/components/navigation/navbar'
import Link from 'next/link'
import { 
  ArrowLeft, 
  Users, 
  BookOpen, 
  TrendingUp, 
  Clock, 
  Award,
  BarChart3,
  PieChart,
  Calendar
} from 'lucide-react'
import { notFound } from 'next/navigation'

interface CourseAnalyticsPageProps {
  params: Promise<{ id: string }>
}

export default async function CourseAnalyticsPage({ params }: CourseAnalyticsPageProps) {
  const session = await requireTeacher()
  const { id: courseId } = await params
  
  // Get course with detailed analytics data
  const course = await prisma.course.findUnique({
    where: { 
      id: courseId,
      teacherId: session.user.id // Ensure teacher owns this course
    },
    include: {
      enrollments: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              createdAt: true,
            }
          }
        },
        orderBy: { enrolledAt: 'desc' }
      },
      modules: {
        include: {
          lessons: {
            include: {
              progress: {
                select: {
                  id: true,
                  isCompleted: true,
                  completedAt: true,
                  userId: true,
                }
              }
            }
          }
        },
        orderBy: { order: 'asc' }
      }
    }
  })

  if (!course) {
    notFound()
  }

  // Calculate analytics
  const totalStudents = course.enrollments.length
  const totalLessons = course.modules.reduce((acc, module) => acc + module.lessons.length, 0)
  
  // Calculate completion rates
  const allProgress = course.modules.flatMap(module => 
    module.lessons.flatMap(lesson => lesson.progress)
  )
  
  const completedLessons = allProgress.filter(p => p.isCompleted).length
  const totalPossibleCompletions = totalStudents * totalLessons
  const overallCompletionRate = totalPossibleCompletions > 0 
    ? (completedLessons / totalPossibleCompletions) * 100 
    : 0

  // Student progress breakdown
  const studentProgress = course.enrollments.map(enrollment => {
    const studentCompletions = allProgress.filter(p => 
      p.userId === enrollment.userId && p.isCompleted
    ).length
    const completionRate = totalLessons > 0 ? (studentCompletions / totalLessons) * 100 : 0
    
    return {
      student: enrollment.user,
      completedLessons: studentCompletions,
      totalLessons,
      completionRate,
      enrolledAt: enrollment.enrolledAt
    }
  })

  // Recent activity (last 30 days)
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
  
  const recentCompletions = allProgress.filter(p => 
    p.isCompleted && p.completedAt && p.completedAt > thirtyDaysAgo
  ).length

  const recentEnrollments = course.enrollments.filter(e =>
    e.enrolledAt > thirtyDaysAgo
  ).length

  // Module completion rates
  const moduleStats = course.modules.map(module => {
    const moduleProgress = module.lessons.flatMap(lesson => lesson.progress)
    const moduleCompletions = moduleProgress.filter(p => p.isCompleted).length
    const modulePossibleCompletions = totalStudents * module.lessons.length
    const moduleCompletionRate = modulePossibleCompletions > 0 
      ? (moduleCompletions / modulePossibleCompletions) * 100 
      : 0

    return {
      id: module.id,
      title: module.title,
      lessonsCount: module.lessons.length,
      completionRate: moduleCompletionRate,
      completedLessons: moduleCompletions,
      totalPossible: modulePossibleCompletions
    }
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/teacher/courses" className="flex items-center text-blue-600 hover:text-blue-500 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to My Courses
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Course Analytics</h1>
              <p className="mt-2 text-gray-600">
                {course.title}
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <Link href={`/teacher/courses/${course.id}/edit`}>
                <Button variant="outline">
                  Edit Course
                </Button>
              </Link>
              <Link href={`/courses/${course.id}`}>
                <Button>
                  View Course
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Students</p>
                  <p className="text-2xl font-bold text-gray-900">{totalStudents}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <BookOpen className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Lessons</p>
                  <p className="text-2xl font-bold text-gray-900">{totalLessons}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{overallCompletionRate.toFixed(1)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Recent Activity</p>
                  <p className="text-2xl font-bold text-gray-900">{recentCompletions}</p>
                  <p className="text-xs text-gray-500">completions (30d)</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Module Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Module Performance
              </CardTitle>
              <CardDescription>
                Completion rates by module
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {moduleStats.map((module, index) => (
                  <div key={module.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">
                        Module {index + 1}: {module.title}
                      </span>
                      <span className="text-sm text-gray-600">
                        {module.completionRate.toFixed(1)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${module.completionRate}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-500">
                      {module.completedLessons} of {module.totalPossible} possible completions
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Student Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Student Progress
              </CardTitle>
              <CardDescription>
                Individual student completion rates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {studentProgress.length > 0 ? (
                  studentProgress.map((student) => (
                    <div key={student.student.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{student.student.name}</p>
                        <p className="text-sm text-gray-600">
                          {student.completedLessons} of {student.totalLessons} lessons completed
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-gray-900">{student.completionRate.toFixed(1)}%</p>
                        <p className="text-xs text-gray-500">
                          Enrolled {student.enrolledAt.toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-gray-600">No students enrolled yet</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Recent Activity (Last 30 Days)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{recentEnrollments}</div>
                <div className="text-sm text-gray-600">New Enrollments</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{recentCompletions}</div>
                <div className="text-sm text-gray-600">Lesson Completions</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">
                  {totalStudents > 0 ? (recentCompletions / totalStudents).toFixed(1) : '0'}
                </div>
                <div className="text-sm text-gray-600">Avg. Completions per Student</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
