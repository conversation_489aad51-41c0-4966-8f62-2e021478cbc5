'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Navbar from '@/components/navigation/navbar'
import { 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle, 
  Play, 
  FileText, 
  HelpCircle,
  Clock,
  Award
} from 'lucide-react'

interface Lesson {
  id: string
  title: string
  content: string
  type: 'TEXT' | 'VIDEO' | 'QUIZ'
  order: number
  videoUrl?: string
  attachments?: string[]
  module: {
    id: string
    title: string
    course: {
      id: string
      title: string
    }
    lessons: Array<{
      id: string
      title: string
      type: string
      order: number
    }>
  }
  quiz?: {
    id: string
    title: string
    description?: string
    timeLimit?: number
    questions: Array<{
      id: string
      points: number
    }>
  }
}

interface Progress {
  isCompleted: boolean
  completedAt?: string
}

export default function LessonPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const courseId = params.courseId as string
  const lessonId = params.lessonId as string

  const [lesson, setLesson] = useState<Lesson | null>(null)
  const [progress, setProgress] = useState<Progress | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchLesson()
  }, [lessonId])

  const fetchLesson = async () => {
    try {
      const response = await fetch(`/api/lessons/${lessonId}`)
      if (response.ok) {
        const data = await response.json()
        setLesson(data.lesson)
        setProgress(data.progress)
      }
    } catch (error) {
      console.error('Error fetching lesson:', error)
    } finally {
      setLoading(false)
    }
  }

  const markAsComplete = async () => {
    try {
      const response = await fetch(`/api/lessons/${lessonId}/complete`, {
        method: 'POST'
      })
      if (response.ok) {
        setProgress({ isCompleted: true, completedAt: new Date().toISOString() })
      }
    } catch (error) {
      console.error('Error marking lesson as complete:', error)
    }
  }

  const getNextLesson = () => {
    if (!lesson) return null
    const currentIndex = lesson.module.lessons.findIndex(l => l.id === lessonId)
    return currentIndex < lesson.module.lessons.length - 1 
      ? lesson.module.lessons[currentIndex + 1] 
      : null
  }

  const getPreviousLesson = () => {
    if (!lesson) return null
    const currentIndex = lesson.module.lessons.findIndex(l => l.id === lessonId)
    return currentIndex > 0 
      ? lesson.module.lessons[currentIndex - 1] 
      : null
  }

  const formatContent = (content: string) => {
    // Simple markdown-like formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">Loading lesson...</div>
        </div>
      </div>
    )
  }

  if (!lesson) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">Lesson not found</div>
        </div>
      </div>
    )
  }

  const nextLesson = getNextLesson()
  const previousLesson = getPreviousLesson()

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={() => router.push(`/courses/${courseId}`)}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Course
          </Button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">{lesson.title}</h1>
              <p className="text-gray-600">
                {lesson.module.course.title} • {lesson.module.title}
              </p>
            </div>
            
            {progress?.isCompleted && (
              <div className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="h-5 w-5" />
                <span className="text-sm font-medium">Completed</span>
              </div>
            )}
          </div>
        </div>

        {/* Lesson Content */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                {lesson.type === 'TEXT' && <FileText className="h-5 w-5 text-blue-500" />}
                {lesson.type === 'VIDEO' && <Play className="h-5 w-5 text-red-500" />}
                {lesson.type === 'QUIZ' && <HelpCircle className="h-5 w-5 text-purple-500" />}
                <CardTitle>{lesson.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              {lesson.type === 'TEXT' && (
                <div 
                  className="prose prose-gray max-w-none"
                  dangerouslySetInnerHTML={{ 
                    __html: `<p>${formatContent(lesson.content)}</p>` 
                  }}
                />
              )}
              
              {lesson.type === 'VIDEO' && lesson.videoUrl && (
                <div className="space-y-4">
                  <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Play className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-600">Video Player</p>
                      <p className="text-sm text-gray-500">{lesson.videoUrl}</p>
                    </div>
                  </div>
                  {lesson.content && (
                    <div 
                      className="prose prose-gray max-w-none"
                      dangerouslySetInnerHTML={{ 
                        __html: `<p>${formatContent(lesson.content)}</p>` 
                      }}
                    />
                  )}
                </div>
              )}
              
              {lesson.type === 'QUIZ' && lesson.quiz && (
                <div className="space-y-4">
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <HelpCircle className="h-8 w-8 text-purple-600" />
                      <div>
                        <h3 className="text-lg font-semibold text-purple-900">
                          {lesson.quiz.title}
                        </h3>
                        {lesson.quiz.description && (
                          <p className="text-purple-700">{lesson.quiz.description}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-6 text-sm text-purple-700 mb-4">
                      <div className="flex items-center space-x-1">
                        <HelpCircle className="h-4 w-4" />
                        <span>{lesson.quiz.questions.length} questions</span>
                      </div>
                      {lesson.quiz.timeLimit && (
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>{lesson.quiz.timeLimit} minutes</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-1">
                        <Award className="h-4 w-4" />
                        <span>
                          {lesson.quiz.questions.reduce((sum, q) => sum + q.points, 0)} points
                        </span>
                      </div>
                    </div>
                    
                    <Button
                      onClick={() => router.push(`/courses/${courseId}/lessons/${lessonId}/quiz`)}
                      className="w-full"
                    >
                      Start Quiz
                    </Button>
                  </div>
                  
                  {lesson.content && (
                    <div 
                      className="prose prose-gray max-w-none"
                      dangerouslySetInnerHTML={{ 
                        __html: `<p>${formatContent(lesson.content)}</p>` 
                      }}
                    />
                  )}
                </div>
              )}
              
              {lesson.attachments && lesson.attachments.length > 0 && (
                <div className="mt-6 pt-6 border-t">
                  <h4 className="font-medium mb-3">Attachments</h4>
                  <div className="space-y-2">
                    {lesson.attachments.map((attachment, index) => (
                      <div key={index} className="flex items-center space-x-2 text-sm">
                        <FileText className="h-4 w-4 text-gray-400" />
                        <span>{attachment}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Lesson Actions */}
          {lesson.type !== 'QUIZ' && !progress?.isCompleted && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <Button onClick={markAsComplete} size="lg">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Mark as Complete
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Navigation */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between">
                <div>
                  {previousLesson && (
                    <Button
                      variant="outline"
                      onClick={() => router.push(`/courses/${courseId}/lessons/${previousLesson.id}`)}
                    >
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Previous: {previousLesson.title}
                    </Button>
                  )}
                </div>
                
                <div>
                  {nextLesson && (
                    <Button
                      onClick={() => router.push(`/courses/${courseId}/lessons/${nextLesson.id}`)}
                    >
                      Next: {nextLesson.title}
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
