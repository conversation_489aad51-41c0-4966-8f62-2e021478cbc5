'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Navbar from '@/components/navigation/navbar'
import { Loading } from '@/components/ui/loading'
import { 
  BookOpen, 
  Users, 
  Clock, 
  Star, 
  Play, 
  CheckCircle,
  Lock,
  MessageSquare
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface Course {
  id: string
  title: string
  description: string
  category: string
  tags: string[]
  price: number
  isPaid: boolean
  isPublished: boolean
  thumbnail?: string
  teacher: {
    id: string
    name: string
    avatar?: string
  }
  modules: Array<{
    id: string
    title: string
    description?: string
    order: number
    lessons: Array<{
      id: string
      title: string
      type: string
      order: number
    }>
  }>
  enrollments: Array<{
    id: string
    userId: string
    progress: number
  }>
  enrollmentCount: number
  lessonCount: number
  createdAt: string
}

export default function CoursePage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const courseId = params.id as string
  const [course, setCourse] = useState<Course | null>(null)
  const [loading, setLoading] = useState(true)
  const [enrolling, setEnrolling] = useState(false)
  const [isEnrolled, setIsEnrolled] = useState(false)

  const fetchCourse = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/courses/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setCourse(data.course)
        
        // Check if user is enrolled
        if (session) {
          const userEnrollment = data.course.enrollments.find(
            (e: any) => e.userId === session.user.id
          )
          setIsEnrolled(!!userEnrollment)
        }
      }
    } catch (error) {
      console.error('Error fetching course:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (params.id) {
      fetchCourse()
    }
  }, [params.id, session])

  const handleEnroll = async () => {
    if (!session) {
      window.location.href = '/login'
      return
    }

    setEnrolling(true)
    try {
      const response = await fetch(`/api/courses/${params.id}/enroll`, {
        method: 'POST',
      })

      if (response.ok) {
        setIsEnrolled(true)
        fetchCourse() // Refresh course data
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to enroll')
      }
    } catch (error) {
      console.error('Enrollment error:', error)
      alert('An error occurred while enrolling')
    } finally {
      setEnrolling(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="flex justify-center items-center h-96">
          <Loading size="lg" />
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Course Not Found</h1>
            <p className="text-gray-600">The course you're looking for doesn't exist.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Course Header */}
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
                  <span className="bg-gray-100 px-2 py-1 rounded">{course.category}</span>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span>4.5</span>
                    <span>({course.enrollmentCount} reviews)</span>
                  </div>
                </div>
                <CardTitle className="text-3xl">{course.title}</CardTitle>
                <CardDescription className="text-lg">
                  {course.description}
                </CardDescription>
                <div className="flex items-center space-x-4 text-sm text-gray-600 mt-4">
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{course.enrollmentCount} students</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{course.lessonCount} lessons</span>
                  </div>
                  <span>by {course.teacher.name}</span>
                </div>
              </CardHeader>
            </Card>

            {/* Course Content */}
            <Card className="bg-white border border-gray-200 shadow-sm">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200">
                <CardTitle className="text-xl font-bold text-gray-900">Course Content</CardTitle>
                <CardDescription className="text-gray-600">
                  {course.modules.length} modules • {course.lessonCount} lessons
                </CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="space-y-4">
                  {course.modules.map((module, moduleIndex) => (
                    <div key={module.id} className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                      <div className="p-4 bg-gradient-to-r from-gray-50 to-blue-50 border-b border-gray-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="text-lg font-bold text-gray-900 mb-1">
                              Module {moduleIndex + 1}: {module.title}
                            </h3>
                            {module.description && (
                              <p className="text-gray-700 mb-2 text-sm">{module.description}</p>
                            )}
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium text-xs">
                              {module.lessons.length} {module.lessons.length === 1 ? 'lesson' : 'lessons'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="divide-y divide-gray-100">
                        {module.lessons.map((lesson, lessonIndex) => (
                          <div key={lesson.id} className="p-3 flex items-center justify-between hover:bg-gray-50 transition-colors">
                            <div className="flex items-center space-x-3">
                              <div className={`p-1.5 rounded-full ${
                                lesson.type === 'VIDEO' ? 'bg-red-100' :
                                lesson.type === 'QUIZ' ? 'bg-purple-100' : 'bg-blue-100'
                              }`}>
                                {lesson.type === 'VIDEO' ? (
                                  <Play className={`h-3.5 w-3.5 ${
                                    lesson.type === 'VIDEO' ? 'text-red-600' :
                                    lesson.type === 'QUIZ' ? 'text-purple-600' : 'text-blue-600'
                                  }`} />
                                ) : lesson.type === 'QUIZ' ? (
                                  <CheckCircle className="h-3.5 w-3.5 text-purple-600" />
                                ) : (
                                  <BookOpen className="h-3.5 w-3.5 text-blue-600" />
                                )}
                              </div>
                              {isEnrolled ? (
                                <button
                                  onClick={() => router.push(`/courses/${course.id}/lessons/${lesson.id}`)}
                                  className="text-left hover:text-blue-600 transition-colors group"
                                >
                                  <div className="font-medium text-gray-900 group-hover:text-blue-600 text-sm">
                                    {lessonIndex + 1}. {lesson.title}
                                  </div>
                                  <div className="text-xs text-gray-500 capitalize">
                                    {lesson.type.toLowerCase().replace('_', ' ')}
                                  </div>
                                </button>
                              ) : (
                                <div>
                                  <div className="font-medium text-gray-700 text-sm">
                                    {lessonIndex + 1}. {lesson.title}
                                  </div>
                                  <div className="text-xs text-gray-500 capitalize">
                                    {lesson.type.toLowerCase().replace('_', ' ')}
                                  </div>
                                </div>
                              )}
                            </div>
                            {!isEnrolled && (
                              <div className="bg-gray-100 p-1.5 rounded-full">
                                <Lock className="h-3.5 w-3.5 text-gray-500" />
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Tags */}
            {course.tags.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Tags</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {course.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Enrollment Card */}
            <Card>
              <CardHeader>
                <div className="aspect-video bg-gray-200 rounded-md flex items-center justify-center mb-4">
                  <Play className="h-12 w-12 text-gray-400" />
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold mb-2">
                    {course.isPaid ? formatCurrency(course.price) : 'Free'}
                  </div>
                  {isEnrolled ? (
                    <div className="space-y-3">
                      <Button className="w-full" size="lg">
                        <BookOpen className="h-4 w-4 mr-2" />
                        Continue Learning
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => router.push(`/courses/${courseId}/discussions`)}
                      >
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Discussions
                      </Button>
                    </div>
                  ) : (
                    <Button
                      className="w-full"
                      size="lg"
                      onClick={handleEnroll}
                      disabled={enrolling}
                    >
                      {enrolling ? (
                        <>
                          <Loading size="sm" className="mr-2" />
                          Enrolling...
                        </>
                      ) : (
                        'Enroll Now'
                      )}
                    </Button>
                  )}
                </div>
              </CardHeader>
            </Card>

            {/* Instructor */}
            <Card>
              <CardHeader>
                <CardTitle>Instructor</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                    <Users className="h-6 w-6 text-gray-500" />
                  </div>
                  <div>
                    <h3 className="font-medium">{course.teacher.name}</h3>
                    <p className="text-sm text-gray-600">Course Instructor</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Course Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Course Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Students Enrolled</span>
                  <span className="font-medium">{course.enrollmentCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Lessons</span>
                  <span className="font-medium">{course.lessonCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Language</span>
                  <span className="font-medium">English</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Last Updated</span>
                  <span className="font-medium">
                    {new Date(course.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
