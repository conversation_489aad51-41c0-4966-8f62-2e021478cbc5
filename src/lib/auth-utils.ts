import { getServerSession } from 'next-auth'
import { authOptions } from './auth'
import { Role } from '@prisma/client'
import { redirect } from 'next/navigation'

export async function getSession() {
  return await getServerSession(authOptions)
}

export async function requireAuth() {
  const session = await getSession()
  if (!session) {
    redirect('/login')
  }
  return session
}

export async function requireRole(allowedRoles: Role[]) {
  const session = await requireAuth()
  if (!allowedRoles.includes(session.user.role as Role)) {
    redirect('/unauthorized')
  }
  return session
}

export async function requireAdmin() {
  return await requireRole([Role.ADMIN])
}

export async function requireTeacher() {
  return await requireRole([Role.TEACHER, Role.ADMIN])
}

export async function requireStudent() {
  return await requireRole([Role.STUDENT, Role.TEACHER, Role.ADMIN])
}
