# E-Learning Platform Architecture (Next.js Prototype)

## Tech Stack
- **Frontend**: Next.js 14 (App Router)
- **Styling**: Tailwind CSS
- **Database**: SQLite (simple for prototype) or PostgreSQL
- **ORM**: Prisma
- **Authentication**: NextAuth.js (simple email/password)
- **File Storage**: Local filesystem (for prototype)
- **State Management**: React Context + useState/useReducer

## Project Structure
```
elearning-platform/
├── src/
│   ├── app/                          # App Router pages
│   │   ├── (auth)/
│   │   │   ├── login/
│   │   │   └── register/
│   │   ├── (dashboard)/
│   │   │   ├── student/
│   │   │   ├── teacher/
│   │   │   └── admin/
│   │   ├── courses/
│   │   │   ├── [id]/
│   │   │   └── create/
│   │   ├── api/                      # API routes
│   │   │   ├── auth/
│   │   │   ├── courses/
│   │   │   ├── users/
│   │   │   └── uploads/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/
│   │   ├── ui/                       # Reusable UI components
│   │   ├── forms/
│   │   ├── navigation/
│   │   └── course/
│   ├── lib/
│   │   ├── auth.ts
│   │   ├── db.ts
│   │   └── utils.ts
│   ├── hooks/
│   ├── context/
│   └── types/
├── prisma/
│   ├── schema.prisma
│   └── migrations/
├── public/
│   └── uploads/                      # File storage
└── package.json
```

## Database Schema (Prisma)

```prisma
// User roles and authentication
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      Role     @default(STUDENT)
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  coursesCreated Course[]         @relation("TeacherCourses")
  enrollments    Enrollment[]
  submissions    Submission[]
  discussions    Discussion[]
  progress       Progress[]
}

enum Role {
  STUDENT
  TEACHER
  ADMIN
}

// Course structure
model Course {
  id          String   @id @default(cuid())
  title       String
  description String
  thumbnail   String?
  category    String
  tags        String[] // Simple array for prototype
  price       Float    @default(0)
  isPaid      Boolean  @default(false)
  isPublished Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  teacherId    String
  teacher      User         @relation("TeacherCourses", fields: [teacherId], references: [id])
  modules      Module[]
  enrollments  Enrollment[]
  discussions  Discussion[]
}

// Course modules/chapters
model Module {
  id          String   @id @default(cuid())
  title       String
  description String?
  order       Int
  createdAt   DateTime @default(now())

  courseId String
  course   Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)
  lessons  Lesson[]
}

// Individual lessons
model Lesson {
  id          String      @id @default(cuid())
  title       String
  content     String      // Rich text content
  videoUrl    String?
  attachments String[]    // File paths
  order       Int
  type        LessonType  @default(TEXT)
  createdAt   DateTime    @default(now())

  moduleId String
  module   Module       @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  quizzes  Quiz[]
  progress Progress[]
}

enum LessonType {
  TEXT
  VIDEO
  QUIZ
  ASSIGNMENT
}

// Student enrollments
model Enrollment {
  id          String   @id @default(cuid())
  enrolledAt  DateTime @default(now())
  completedAt DateTime?
  progress    Float    @default(0) // Percentage

  userId   String
  user     User   @relation(fields: [userId], references: [id])
  courseId String
  course   Course @relation(fields: [courseId], references: [id])

  @@unique([userId, courseId])
}

// Progress tracking
model Progress {
  id          String   @id @default(cuid())
  isCompleted Boolean  @default(false)
  completedAt DateTime?
  timeSpent   Int      @default(0) // in minutes

  userId   String
  user     User   @relation(fields: [userId], references: [id])
  lessonId String
  lesson   Lesson @relation(fields: [lessonId], references: [id])

  @@unique([userId, lessonId])
}

// Quiz system
model Quiz {
  id          String     @id @default(cuid())
  title       String
  description String?
  questions   Question[]
  timeLimit   Int?       // in minutes
  createdAt   DateTime   @default(now())

  lessonId    String
  lesson      Lesson       @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  submissions Submission[]
}

model Question {
  id            String       @id @default(cuid())
  text          String
  type          QuestionType @default(MULTIPLE_CHOICE)
  options       String[]     // For multiple choice
  correctAnswer String
  points        Int          @default(1)

  quizId String
  quiz   Quiz   @relation(fields: [quizId], references: [id], onDelete: Cascade)
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  SHORT_ANSWER
}

// Quiz submissions
model Submission {
  id          String   @id @default(cuid())
  answers     Json     // Store answers as JSON
  score       Float?
  submittedAt DateTime @default(now())

  userId String
  user   User   @relation(fields: [userId], references: [id])
  quizId String
  quiz   Quiz   @relation(fields: [quizId], references: [id])
}

// Discussion forums
model Discussion {
  id        String   @id @default(cuid())
  title     String
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  authorId String
  author   User   @relation(fields: [authorId], references: [id])
  courseId String
  course   Course @relation(fields: [courseId], references: [id])
  replies  Reply[]
}

model Reply {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())

  authorId     String
  author       User       @relation(fields: [authorId], references: [id])
  discussionId String
  discussion   Discussion @relation(fields: [discussionId], references: [id])
}
```

## Key Components Architecture

### 1. Authentication Flow
```typescript
// lib/auth.ts
export const authConfig = {
  providers: [
    CredentialsProvider({
      // Simple email/password auth
    })
  ],
  callbacks: {
    jwt: ({ token, user }) => {
      if (user) token.role = user.role;
      return token;
    },
    session: ({ session, token }) => {
      session.user.role = token.role;
      return session;
    }
  }
}
```

### 2. Role-Based Access Control
```typescript
// Higher-order component for protecting routes
export function withAuth(WrappedComponent, allowedRoles) {
  return function AuthenticatedComponent(props) {
    const { data: session } = useSession();
    
    if (!session) {
      redirect('/login');
    }
    
    if (!allowedRoles.includes(session.user.role)) {
      return <UnauthorizedPage />;
    }
    
    return <WrappedComponent {...props} />;
  };
}
```

### 3. File Upload System
```typescript
// Simple file upload for prototype
export async function uploadFile(file: File, folder: string) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('folder', folder);
  
  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData,
  });
  
  return response.json();
}
```

## Page Structure

### Student Dashboard
- **Course Catalog**: Browse and search courses
- **My Courses**: Enrolled courses with progress
- **Certificates**: Completed course certificates
- **Discussion**: Course forums

### Teacher Dashboard
- **Course Management**: Create/edit courses
- **Content Upload**: Add lessons, videos, files
- **Student Analytics**: Basic engagement metrics
- **Quiz Builder**: Create assessments

### Admin Panel
- **User Management**: View/manage users
- **Course Approval**: Publish/unpublish courses
- **Site Analytics**: Basic usage stats
- **Content Moderation**: Manage discussions

## API Routes Structure

```
/api/
├── auth/[...nextauth].ts         # Authentication
├── courses/
│   ├── index.ts                  # GET all courses, POST new course
│   ├── [id]/
│   │   ├── index.ts             # GET/PUT course details
│   │   ├── enroll.ts            # POST enroll student
│   │   └── progress.ts          # GET/PUT progress
├── users/
│   ├── profile.ts               # GET/PUT user profile
│   └── [id]/courses.ts          # GET user's courses
├── quizzes/
│   ├── [id]/submit.ts           # POST quiz submission
│   └── [id]/results.ts          # GET quiz results
└── upload.ts                    # File upload handler
```

## State Management

### Course Context
```typescript
const CourseContext = createContext({
  currentCourse: null,
  progress: {},
  updateProgress: () => {},
  enrollments: []
});
```

### User Context
```typescript
const UserContext = createContext({
  user: null,
  preferences: {},
  updatePreferences: () => {}
});
```

## Key Features Implementation

### 1. Progress Tracking
- Store completion status in Progress model
- Calculate percentage based on completed lessons
- Update in real-time as user progresses

### 2. Quiz System
- Dynamic question rendering
- Automatic scoring for multiple choice
- Manual review for open-ended questions

### 3. File Management
- Local file storage in `/public/uploads/`
- Organized by course/lesson/user folders
- Simple file serving through Next.js static files

### 4. Discussion Forums
- Thread-based discussions per course
- Simple nested reply system
- Teacher moderation capabilities

## Deployment Considerations (Prototype)

### Development
- SQLite database for simplicity
- Local file storage
- No CDN or external services

### Simple Production
- PostgreSQL on Vercel/Railway
- File storage on Vercel (with limitations)
- Environment variables for config

This architecture prioritizes simplicity and rapid prototyping while maintaining a clean structure that could be extended for production use later.